from tortoise import BaseDBAsync<PERSON><PERSON>


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `app` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL UNIQUE,
    `description` VA<PERSON>HAR(255) NOT NULL,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `api_key` VARCHAR(255) NOT NULL UNIQUE
) CHARACTER SET utf8mb4 COMMENT='应用主表';
CREATE TABLE IF NOT EXISTS `device` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHA<PERSON>(255) NOT NULL UNIQUE,
    `description` VARCHA<PERSON>(255) NOT NULL,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `is_active` BOOL NOT NULL DEFAULT 1
) CHARACTER SET utf8mb4 COMMENT='设备表';
CREATE TABLE IF NOT EXISTS `device_app` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `app_id` INT NOT NULL,
    `device_id` INT NOT NULL,
    UNIQUE KEY `uid_device_app_device__554ba4` (`device_id`, `app_id`),
    CONSTRAINT `fk_device_a_app_0160826c` FOREIGN KEY (`app_id`) REFERENCES `app` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_device_a_device_0949943f` FOREIGN KEY (`device_id`) REFERENCES `device` (`id`) ON DELETE CASCADE
) CHARACTER SET utf8mb4 COMMENT='设备应用关联表';
CREATE TABLE IF NOT EXISTS `aerich` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `version` VARCHAR(255) NOT NULL,
    `app` VARCHAR(100) NOT NULL,
    `content` JSON NOT NULL
) CHARACTER SET utf8mb4;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
