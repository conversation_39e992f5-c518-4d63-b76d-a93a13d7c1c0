#!/usr/bin/env python3
"""
管理系统启动脚本 - 集成版本
"""
import uvicorn
from app.factory import create_app
from settings.config import settings

if __name__ == "__main__":
    app = create_app()
    
    print("🚀 启动集成管理系统...")
    print(f"📍 访问地址: http://localhost:{settings.fastapi_port}")
    print(f"📖 API文档: http://localhost:{settings.fastapi_port}/docs")
    print(f"🔐 管理后台API: http://localhost:{settings.fastapi_port}/api/admin/")
    print("👤 默认账号: admin / 123456")
    print("-" * 50)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=settings.fastapi_port,
        reload=True,
        log_level="info"
    )
