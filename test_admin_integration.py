#!/usr/bin/env python3
"""
管理系统集成测试脚本
"""
import asyncio
import sys
from tortoise import Tortoise
from settings.tortoise_config import TORTOISE_ORM


async def test_admin_models():
    """测试管理系统模型是否正确导入"""
    try:
        print("🔄 测试管理系统模型导入...")
        from api_versions.admin.models import (
            AdminUser, AdminRole, AdminMenu, AdminApi, AdminDept, AdminAuditLog
        )
        print("✅ 管理系统模型导入成功")
        return True
    except Exception as e:
        print(f"❌ 管理系统模型导入失败: {e}")
        return False


async def test_admin_schemas():
    """测试管理系统 schemas 是否正确导入"""
    try:
        print("🔄 测试管理系统 schemas 导入...")
        from api_versions.admin.schemas import (
            Success, UserCreate, RoleCreate, MenuCreate, ApiCreate, DeptCreate
        )
        print("✅ 管理系统 schemas 导入成功")
        return True
    except Exception as e:
        print(f"❌ 管理系统 schemas 导入失败: {e}")
        return False


async def test_admin_controllers():
    """测试管理系统控制器是否正确导入"""
    try:
        print("🔄 测试管理系统控制器导入...")
        from api_versions.admin.controllers import (
            user_controller, role_controller, menu_controller, 
            api_controller, dept_controller, auth_controller
        )
        print("✅ 管理系统控制器导入成功")
        return True
    except Exception as e:
        print(f"❌ 管理系统控制器导入失败: {e}")
        return False


async def test_admin_routers():
    """测试管理系统路由是否正确导入"""
    try:
        print("🔄 测试管理系统路由导入...")
        from api_versions.admin.routers import router
        print(f"✅ 管理系统路由导入成功，包含 {len(router.routes)} 个路由")
        return True
    except Exception as e:
        print(f"❌ 管理系统路由导入失败: {e}")
        return False


async def test_database_connection():
    """测试数据库连接"""
    try:
        print("🔄 测试数据库连接...")
        await Tortoise.init(config=TORTOISE_ORM)
        
        # 测试连接
        conn = Tortoise.get_connection("default")
        await conn.execute_query("SELECT 1")
        
        print("✅ 数据库连接成功")
        await Tortoise.close_connections()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


async def test_app_factory():
    """测试应用工厂是否正确集成管理路由"""
    try:
        print("🔄 测试应用工厂集成...")
        from app.factory import create_app
        
        app = create_app()
        
        # 检查是否包含管理路由
        admin_routes = [route for route in app.routes if hasattr(route, 'path') and '/api/admin' in route.path]
        
        if admin_routes:
            print(f"✅ 应用工厂集成成功，包含 {len(admin_routes)} 个管理路由")
        else:
            print("⚠️ 应用工厂集成成功，但未找到管理路由")
        
        return True
    except Exception as e:
        print(f"❌ 应用工厂集成失败: {e}")
        return False


async def test_password_utils():
    """测试密码工具"""
    try:
        print("🔄 测试密码工具...")
        from api_versions.admin.core import PasswordUtils
        
        password = "test123"
        hashed = PasswordUtils.get_password_hash(password)
        verified = PasswordUtils.verify_password(password, hashed)
        
        if verified:
            print("✅ 密码工具测试成功")
            return True
        else:
            print("❌ 密码验证失败")
            return False
    except Exception as e:
        print(f"❌ 密码工具测试失败: {e}")
        return False


async def test_jwt_utils():
    """测试 JWT 工具"""
    try:
        print("🔄 测试 JWT 工具...")
        from api_versions.admin.core import JWTUtils
        
        data = {"sub": "test_user", "username": "test"}
        token = JWTUtils.create_access_token(data)
        payload = JWTUtils.verify_token(token)
        
        if payload and payload.get("sub") == "test_user":
            print("✅ JWT 工具测试成功")
            return True
        else:
            print("❌ JWT 验证失败")
            return False
    except Exception as e:
        print(f"❌ JWT 工具测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始管理系统集成测试...")
    print("=" * 50)
    
    tests = [
        test_admin_models,
        test_admin_schemas,
        test_admin_controllers,
        test_admin_routers,
        test_app_factory,
        test_password_utils,
        test_jwt_utils,
        test_database_connection,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            result = await test()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        print("-" * 30)
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！管理系统集成成功！")
        print("\n📝 下一步操作:")
        print("1. 运行数据库迁移: python migrate_admin.py")
        print("2. 启动服务: python run_admin_system.py")
        print("3. 访问 API 文档: http://localhost:8000/docs")
        print("4. 使用默认账号登录: admin / 123456")
        return True
    else:
        print("⚠️ 部分测试失败，请检查错误信息并修复")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(run_all_tests())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n❌ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行异常: {e}")
        sys.exit(1)
