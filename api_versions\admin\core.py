from typing import Generic, TypeVar, Type, Optional, List, Dict, Any
from tortoise.models import Model
from tortoise.queryset import QuerySet
from pydantic import BaseModel
from passlib.context import CryptContext
import jwt
from datetime import datetime, timedelta
from settings.config import settings

# Generic types for CRUD operations
ModelType = TypeVar("ModelType", bound=Model)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
SECRET_KEY = "your-secret-key-change-this-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7 days


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        self.model = model

    async def get(self, id: int) -> Optional[ModelType]:
        return await self.model.filter(id=id).first()

    async def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        **filters
    ) -> List[ModelType]:
        query = self.model.filter(**filters)
        return await query.offset(skip).limit(limit).all()

    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        obj_data = obj_in.model_dump() if hasattr(obj_in, 'model_dump') else obj_in.dict()
        return await self.model.create(**obj_data)

    async def update(
        self, 
        db_obj: ModelType, 
        obj_in: UpdateSchemaType | Dict[str, Any]
    ) -> ModelType:
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True) if hasattr(obj_in, 'model_dump') else obj_in.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        await db_obj.save()
        return db_obj

    async def remove(self, id: int) -> ModelType:
        obj = await self.model.filter(id=id).first()
        if obj:
            await obj.delete()
        return obj

    async def count(self, **filters) -> int:
        return await self.model.filter(**filters).count()

    async def exists(self, **filters) -> bool:
        return await self.model.filter(**filters).exists()


class PasswordUtils:
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        return pwd_context.hash(password)


class JWTUtils:
    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt

    @staticmethod
    def verify_token(token: str) -> Optional[dict]:
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            return payload
        except jwt.PyJWTError:
            return None


class PermissionChecker:
    @staticmethod
    async def check_api_permission(user_id: int, path: str, method: str) -> bool:
        """检查用户是否有访问特定API的权限"""
        from .models import AdminUser, AdminApi
        
        user = await AdminUser.filter(id=user_id).prefetch_related("roles__apis").first()
        if not user:
            return False
        
        # 超级用户拥有所有权限
        if user.is_superuser:
            return True
        
        # 检查用户角色是否有该API权限
        for role in user.roles:
            for api in role.apis:
                if api.path == path and api.method == method:
                    return True
        
        return False

    @staticmethod
    async def get_user_permissions(user_id: int) -> Dict[str, List[str]]:
        """获取用户的所有权限"""
        from .models import AdminUser
        
        user = await AdminUser.filter(id=user_id).prefetch_related(
            "roles__apis", "roles__menus"
        ).first()
        
        if not user:
            return {"apis": [], "menus": []}
        
        apis = []
        menus = []
        
        if user.is_superuser:
            # 超级用户获取所有权限
            from .models import AdminApi, AdminMenu
            all_apis = await AdminApi.all()
            all_menus = await AdminMenu.all()
            apis = [f"{api.method}:{api.path}" for api in all_apis]
            menus = [menu.path for menu in all_menus]
        else:
            # 普通用户根据角色获取权限
            for role in user.roles:
                for api in role.apis:
                    apis.append(f"{api.method}:{api.path}")
                for menu in role.menus:
                    menus.append(menu.path)
        
        return {
            "apis": list(set(apis)),
            "menus": list(set(menus))
        }


class TreeBuilder:
    @staticmethod
    def build_tree(items: List[Dict], parent_id_key: str = "parent_id", id_key: str = "id") -> List[Dict]:
        """构建树形结构"""
        tree = []
        item_map = {item[id_key]: item for item in items}
        
        for item in items:
            parent_id = item.get(parent_id_key, 0)
            if parent_id == 0 or parent_id not in item_map:
                # 根节点
                tree.append(item)
            else:
                # 子节点
                parent = item_map[parent_id]
                if "children" not in parent:
                    parent["children"] = []
                parent["children"].append(item)
        
        return tree
