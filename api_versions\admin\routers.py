from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Optional, Dict, Any
from .controllers import (
    user_controller, role_controller, menu_controller, 
    api_controller, dept_controller, auth_controller
)
from .schemas import (
    Success, ErrorResponse, UserCreate, UserUpdate, UpdatePassword,
    RoleCreate, RoleUpdate, RoleUpdateMenusApis,
    MenuCreate, MenuUpdate, ApiCreate, ApiUpdate,
    DeptCreate, DeptUpdate, LoginForm, Token, UserInfo
)
from .models import AdminUser

# 创建路由器
router = APIRouter()

# 安全认证
security = HTTPBearer()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> AdminUser:
    """获取当前用户"""
    user = await auth_controller.get_current_user(credentials.credentials)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user


async def get_current_active_user(current_user: AdminUser = Depends(get_current_user)) -> AdminUser:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


# 认证相关路由
@router.post("/auth/login", response_model=Dict[str, Any], summary="用户登录")
async def login(login_form: LoginForm):
    try:
        result = await auth_controller.login(login_form)
        return Success(data=result).model_dump()
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/auth/user_info", response_model=Dict[str, Any], summary="获取用户信息")
async def get_user_info(current_user: AdminUser = Depends(get_current_active_user)):
    from .core import PermissionChecker
    permissions = await PermissionChecker.get_user_permissions(current_user.id)
    
    user_info = {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "alias": current_user.alias,
        "phone": current_user.phone,
        "is_active": current_user.is_active,
        "is_superuser": current_user.is_superuser,
        "last_login": current_user.last_login,
        "dept_id": current_user.dept_id,
        "permissions": permissions
    }
    
    return Success(data=user_info).model_dump()


@router.get("/auth/menus", response_model=Dict[str, Any], summary="获取用户菜单")
async def get_user_menus(current_user: AdminUser = Depends(get_current_active_user)):
    menus = await menu_controller.get_user_menus(current_user.id)
    return Success(data=menus).model_dump()


# 用户管理路由
@router.get("/users", response_model=Dict[str, Any], summary="获取用户列表")
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    username: Optional[str] = Query(None),
    email: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    current_user: AdminUser = Depends(get_current_active_user)
):
    filters = {}
    if username:
        filters["username__icontains"] = username
    if email:
        filters["email__icontains"] = email
    if is_active is not None:
        filters["is_active"] = is_active
    
    users = await user_controller.get_multi(skip=skip, limit=limit, **filters)
    total = await user_controller.count(**filters)
    
    user_list = []
    for user in users:
        user_data = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "alias": user.alias,
            "phone": user.phone,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "last_login": user.last_login,
            "dept_id": user.dept_id,
            "created_at": user.created_at,
            "updated_at": user.updated_at
        }
        user_list.append(user_data)
    
    return Success(data={"items": user_list, "total": total}).model_dump()


@router.post("/users", response_model=Dict[str, Any], summary="创建用户")
async def create_user(
    user_create: UserCreate,
    current_user: AdminUser = Depends(get_current_active_user)
):
    try:
        user = await user_controller.create_user(user_create)
        return Success(data={"id": user.id}).model_dump()
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/users/{user_id}", response_model=Dict[str, Any], summary="更新用户")
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: AdminUser = Depends(get_current_active_user)
):
    user = await user_controller.get(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 更新用户基本信息
    update_data = user_update.model_dump(exclude_unset=True, exclude={"id", "role_ids"})
    await user_controller.update(user, update_data)
    
    # 更新用户角色
    if hasattr(user_update, 'role_ids') and user_update.role_ids is not None:
        await user_controller.update_user_roles(user_id, user_update.role_ids)
    
    return Success(message="用户更新成功").model_dump()


@router.delete("/users/{user_id}", response_model=Dict[str, Any], summary="删除用户")
async def delete_user(
    user_id: int,
    current_user: AdminUser = Depends(get_current_active_user)
):
    user = await user_controller.remove(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return Success(message="用户删除成功").model_dump()


@router.put("/users/{user_id}/password", response_model=Dict[str, Any], summary="修改密码")
async def update_password(
    user_id: int,
    password_update: UpdatePassword,
    current_user: AdminUser = Depends(get_current_active_user)
):
    # 只能修改自己的密码或超级用户可以修改任何人的密码
    if current_user.id != user_id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足")
    
    success = await user_controller.update_password(
        user_id, 
        password_update.old_password, 
        password_update.new_password
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="原密码错误")
    
    return Success(message="密码修改成功").model_dump()


# 角色管理路由
@router.get("/roles", response_model=Dict[str, Any], summary="获取角色列表")
async def get_roles(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    name: Optional[str] = Query(None),
    current_user: AdminUser = Depends(get_current_active_user)
):
    filters = {}
    if name:
        filters["name__icontains"] = name
    
    roles = await role_controller.get_multi(skip=skip, limit=limit, **filters)
    total = await role_controller.count(**filters)
    
    role_list = []
    for role in roles:
        role_data = {
            "id": role.id,
            "name": role.name,
            "desc": role.desc,
            "created_at": role.created_at,
            "updated_at": role.updated_at
        }
        role_list.append(role_data)
    
    return Success(data={"items": role_list, "total": total}).model_dump()


@router.post("/roles", response_model=Dict[str, Any], summary="创建角色")
async def create_role(
    role_create: RoleCreate,
    current_user: AdminUser = Depends(get_current_active_user)
):
    if await role_controller.is_exist(role_create.name):
        raise HTTPException(status_code=400, detail="角色名称已存在")
    
    role = await role_controller.create(role_create)
    return Success(data={"id": role.id}).model_dump()


@router.put("/roles/{role_id}", response_model=Dict[str, Any], summary="更新角色")
async def update_role(
    role_id: int,
    role_update: RoleUpdate,
    current_user: AdminUser = Depends(get_current_active_user)
):
    role = await role_controller.get(role_id)
    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    update_data = role_update.model_dump(exclude_unset=True, exclude={"id"})
    await role_controller.update(role, update_data)
    
    return Success(message="角色更新成功").model_dump()


@router.delete("/roles/{role_id}", response_model=Dict[str, Any], summary="删除角色")
async def delete_role(
    role_id: int,
    current_user: AdminUser = Depends(get_current_active_user)
):
    role = await role_controller.remove(role_id)
    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    return Success(message="角色删除成功").model_dump()


@router.put("/roles/{role_id}/permissions", response_model=Dict[str, Any], summary="更新角色权限")
async def update_role_permissions(
    role_id: int,
    permissions: RoleUpdateMenusApis,
    current_user: AdminUser = Depends(get_current_active_user)
):
    try:
        await role_controller.update_role_permissions(
            role_id,
            permissions.menu_ids,
            permissions.api_infos
        )
        return Success(message="角色权限更新成功").model_dump()
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


# 菜单管理路由
@router.get("/menus", response_model=Dict[str, Any], summary="获取菜单列表")
async def get_menus(
    current_user: AdminUser = Depends(get_current_active_user)
):
    menus = await menu_controller.get_menu_tree()
    return Success(data=menus).model_dump()


@router.post("/menus", response_model=Dict[str, Any], summary="创建菜单")
async def create_menu(
    menu_create: MenuCreate,
    current_user: AdminUser = Depends(get_current_active_user)
):
    menu = await menu_controller.create(menu_create)
    return Success(data={"id": menu.id}).model_dump()


@router.put("/menus/{menu_id}", response_model=Dict[str, Any], summary="更新菜单")
async def update_menu(
    menu_id: int,
    menu_update: MenuUpdate,
    current_user: AdminUser = Depends(get_current_active_user)
):
    menu = await menu_controller.get(menu_id)
    if not menu:
        raise HTTPException(status_code=404, detail="菜单不存在")

    update_data = menu_update.model_dump(exclude_unset=True, exclude={"id"})
    await menu_controller.update(menu, update_data)

    return Success(message="菜单更新成功").model_dump()


@router.delete("/menus/{menu_id}", response_model=Dict[str, Any], summary="删除菜单")
async def delete_menu(
    menu_id: int,
    current_user: AdminUser = Depends(get_current_active_user)
):
    menu = await menu_controller.remove(menu_id)
    if not menu:
        raise HTTPException(status_code=404, detail="菜单不存在")

    return Success(message="菜单删除成功").model_dump()


# API管理路由
@router.get("/apis", response_model=Dict[str, Any], summary="获取API列表")
async def get_apis(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    path: Optional[str] = Query(None),
    method: Optional[str] = Query(None),
    tags: Optional[str] = Query(None),
    current_user: AdminUser = Depends(get_current_active_user)
):
    filters = {}
    if path:
        filters["path__icontains"] = path
    if method:
        filters["method"] = method.upper()
    if tags:
        filters["tags__icontains"] = tags

    apis = await api_controller.get_multi(skip=skip, limit=limit, **filters)
    total = await api_controller.count(**filters)

    api_list = []
    for api in apis:
        api_data = {
            "id": api.id,
            "path": api.path,
            "method": api.method,
            "summary": api.summary,
            "tags": api.tags,
            "created_at": api.created_at,
            "updated_at": api.updated_at
        }
        api_list.append(api_data)

    return Success(data={"items": api_list, "total": total}).model_dump()


@router.post("/apis", response_model=Dict[str, Any], summary="创建API")
async def create_api(
    api_create: ApiCreate,
    current_user: AdminUser = Depends(get_current_active_user)
):
    api = await api_controller.create(api_create)
    return Success(data={"id": api.id}).model_dump()


@router.put("/apis/{api_id}", response_model=Dict[str, Any], summary="更新API")
async def update_api(
    api_id: int,
    api_update: ApiUpdate,
    current_user: AdminUser = Depends(get_current_active_user)
):
    api = await api_controller.get(api_id)
    if not api:
        raise HTTPException(status_code=404, detail="API不存在")

    update_data = api_update.model_dump(exclude_unset=True, exclude={"id"})
    await api_controller.update(api, update_data)

    return Success(message="API更新成功").model_dump()


@router.delete("/apis/{api_id}", response_model=Dict[str, Any], summary="删除API")
async def delete_api(
    api_id: int,
    current_user: AdminUser = Depends(get_current_active_user)
):
    api = await api_controller.remove(api_id)
    if not api:
        raise HTTPException(status_code=404, detail="API不存在")

    return Success(message="API删除成功").model_dump()


@router.post("/apis/sync", response_model=Dict[str, Any], summary="同步API")
async def sync_apis(
    request: Request,
    current_user: AdminUser = Depends(get_current_active_user)
):
    # 获取OpenAPI数据
    openapi_data = request.app.openapi()
    synced_count = await api_controller.sync_apis_from_openapi(openapi_data)

    return Success(
        message=f"API同步成功，新增{synced_count}个API",
        data={"synced_count": synced_count}
    ).model_dump()


# 部门管理路由
@router.get("/depts", response_model=Dict[str, Any], summary="获取部门列表")
async def get_depts(
    current_user: AdminUser = Depends(get_current_active_user)
):
    depts = await dept_controller.get_dept_tree()
    return Success(data=depts).model_dump()


@router.post("/depts", response_model=Dict[str, Any], summary="创建部门")
async def create_dept(
    dept_create: DeptCreate,
    current_user: AdminUser = Depends(get_current_active_user)
):
    dept = await dept_controller.create(dept_create)
    return Success(data={"id": dept.id}).model_dump()


@router.put("/depts/{dept_id}", response_model=Dict[str, Any], summary="更新部门")
async def update_dept(
    dept_id: int,
    dept_update: DeptUpdate,
    current_user: AdminUser = Depends(get_current_active_user)
):
    dept = await dept_controller.get(dept_id)
    if not dept:
        raise HTTPException(status_code=404, detail="部门不存在")

    update_data = dept_update.update_dict()
    await dept_controller.update(dept, update_data)

    return Success(message="部门更新成功").model_dump()


@router.delete("/depts/{dept_id}", response_model=Dict[str, Any], summary="删除部门")
async def delete_dept(
    dept_id: int,
    current_user: AdminUser = Depends(get_current_active_user)
):
    dept = await dept_controller.get(dept_id)
    if not dept:
        raise HTTPException(status_code=404, detail="部门不存在")

    # 软删除
    dept.is_deleted = True
    await dept.save()

    return Success(message="部门删除成功").model_dump()
