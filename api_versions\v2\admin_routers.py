from fastapi import APIRouter, Depends, HTTPException, status, Request, Body
from fastapi.security import <PERSON>Auth2<PERSON><PERSON>wordBearer, OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import jwt
from datetime import datetime, timedelta
import os
import sys
from settings.config import settings
from .env_manager import env_manager
from .models import Device, App

# 创建路由器
router = APIRouter()

# 定义模型
class Token(BaseModel):
    access_token: str
    token_type: str

class UserInfo(BaseModel):
    id: int
    username: str
    email: str
    is_active: bool = True
    is_superuser: bool = False
    avatar: Optional[str] = None

class MenuItem(BaseModel):
    id: int
    name: str
    path: str
    component: str
    redirect: Optional[str] = None
    meta: dict
    children: Optional[List['MenuItem']] = None

# 创建一个密钥
SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7天

# 模拟用户数据
USERS = {
    "admin": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "password": "123456",
        "is_active": True,
        "is_superuser": True,
        "avatar": None
    }
}

# 创建token
def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# 登录接口
@router.post("/v1/base/access_token")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    print(f"Login attempt: {form_data.username}")
    user = USERS.get(form_data.username)
    if not user or form_data.password != user["password"]:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# 获取用户信息
@router.get("/v1/base/userinfo")
async def get_user_info(request: Request):
    print("Getting user info")
    # 这里应该从token中获取用户信息，但为了简化，我们直接返回admin用户
    user = USERS["admin"]
    return UserInfo(
        id=user["id"],
        username=user["username"],
        email=user["email"],
        is_active=user["is_active"],
        is_superuser=user["is_superuser"],
        avatar=user["avatar"]
    )

# 获取用户菜单
@router.get("/v1/base/usermenu")
async def get_user_menu():
    print("Getting user menu")
    # 返回一些示例菜单
    return [
        {
            "id": 1,
            "name": "系统管理",
            "path": "/system",
            "component": "Layout",
            "redirect": "/system/user",
            "meta": {
                "title": "系统管理",
                "icon": "carbon:gui-management",
                "order": 1,
                "hideMenu": False,
            },
            "children": [
                {
                    "id": 2,
                    "name": "用户管理",
                    "path": "user",
                    "component": "/system/user",
                    "meta": {
                        "title": "用户管理",
                        "icon": "material-symbols:person-outline-rounded",
                        "order": 1,
                        "hideMenu": False,
                    }
                },
                {
                    "id": 3,
                    "name": "角色管理",
                    "path": "role",
                    "component": "/system/role",
                    "meta": {
                        "title": "角色管理",
                        "icon": "carbon:user-role",
                        "order": 2,
                        "hideMenu": False,
                    }
                }
            ]
        }
    ]

# 获取用户API权限
@router.get("/v1/base/userapi")
async def get_user_api():
    print("Getting user API")
    # 返回用户可访问的API列表
    return [
        "/v1/base/userinfo",
        "/v1/base/usermenu",
        "/v1/base/userapi"
    ]

# 虚拟环境信息接口
@router.get("/environment/info")
async def get_environment_info():
    return env_manager.get_env_info()

# 虚拟环境控制接口
@router.post("/environment/control")
async def control_environment(action: str = Body(..., embed=True)):
    if action == "restart":
        return await env_manager.restart_service()
    elif action == "update":
        return {"status": "success", "message": "依赖更新指令已发送"}
    else:
        raise HTTPException(status_code=400, detail="不支持的操作")

# 更新环境变量接口
@router.post("/environment/update-env")
async def update_environment_var(var_name: str = Body(...), var_value: str = Body(...)):
    return await env_manager.update_env_var(var_name, var_value)

# 设备列表接口
@router.get("/device/list")
async def get_device_list():
    try:
        # 使用现有的v2接口获取设备列表
        devices = await Device.all()
        device_list = []
        
        for device in devices:
            device_list.append({
                "id": device.id,
                "name": device.name,
                "description": device.description,
                "status": "active",  # 可以根据实际情况设置状态
                "created_at": device.created_at.strftime("%Y-%m-%d %H:%M:%S") if device.created_at else ""
            })
        
        return {
            "items": device_list,
            "total": len(device_list)
        }
    except Exception as e:
        print(f"Error getting devices: {e}")
        return {
            "items": [],
            "total": 0
        }