import asyncio
import colorama
from fastapi import APIRouter, Request, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from core.logger import logger
from utils.redis_tools import generate_cache_key, get_cached_sse_data, store_sse_bulk_data
from core.services.v2 import stt_server, llm_server, tts_server, llm_server_other
from core.redis_client import redis_client
import orjson
import time
from .schema import DeviceCreateSchema, DeviceUpdateSchema, AppWithKeySchema, Device_Pydantic, App_Pydantic, DeviceWithAppsSchema
from .models import Device, App
from .admin_routers import router as admin_router

router = APIRouter()


@router.post("/audio-to-text", description="最大支持15MB的音频文件", summary="语音转文本接口")
async def audio_to_text(result:str=Depends(stt_server.audio_to_text)):
    return result


@router.post("/chat-messages-blocking", description="阻塞模式 llm", summary="阻塞模式 llm")
async def chat_messages_blocking(request:Request, text:str):
    result_json = await llm_server.chat_messages_block(request=request, text=text)
    return result_json

@router.post("/chat-messages-streaming", description="流模式 llm", summary="流模式 llm", include_in_schema=False)
async def chat_messages_streaming(request:Request, text:str):
    async for data in  llm_server.chat_messages_streaming(request=request, text=text):
        yield data


@router.post("/text-to-audio", description="文本转语音接口", summary="文本转语音")
async def text_to_audio(request:Request, text:str):
    return await tts_server.text_to_audio_(request=request, text=text)


@router.get("/parameters", description="可能为空", summary="获取开场白和建议问题", include_in_schema=False)
async def get_parameters(request:Request):
    return StreamingResponse(
        llm_server_other.parameters_(request=request),
        media_type='text/event-stream',
        headers={"X-Stream-Data": "true"}
    )

@router.post("/tts-blocking", description="阻塞模式主入口", summary="通过传递音频获取全部(阻塞模式)")
async def main_router(request:Request, text:str=Depends(stt_server.audio_to_text)):
    print(text)

    llm_result = await chat_messages_blocking(request=request, text=text)
    print(llm_result)

    tts_url = await text_to_audio(request=request, text=llm_result)
    print(tts_url)


    return {
        "question": text,
        "answer": llm_result,
        "url": tts_url
    }


# async def generate_stream(*, request, text):


#     # 测试不写缓存的速度
#     # async for data in llm_server.chat_messages_streaming(request=request, text=text):
#     #     yield data

#     # async for parameter in llm_server_other.parameters_(request=request):
#         # yield parameter


#     # 写缓存的速度

#     cache_key = await generate_cache_key(request=request, text=text)
#     cached_data = await get_cached_sse_data(request=request, cache_key=cache_key)

#     if cached_data:
#         logger.info(f"🎯 命中SSE缓存(哈希: {cache_key[-10:]})")
#         for data in cached_data:
#             yield data
#     else:
#         logger.info(f"💾 处理新的请求并进行缓存(哈希: {cache_key[-10:]})")
#         async for data in llm_server.chat_messages_streaming(request=request, text=text):
#             yield data
#             await store_see_data_to_cache(request=request, cache_key=cache_key, sse_data=data)

#         async for parameter in llm_server_other.parameters_(request=request):
#             yield parameter
#             await store_see_data_to_cache(request=request, cache_key=cache_key, sse_data=parameter)

@router.get("/llm-streaming", description="流模式 llm", summary="流模式 llm")
async def llm_streaming(*, request:Request, text:str):

    text = text.strip("？?。.>")

    return StreamingResponse(
        generate_stream(request=request, text=text), 
        media_type='text/event-stream',
        headers={"X-Stream-Data": "true"}
    )


async def generate_stream(*, request, text, skip_question=False):
    cache_key = await generate_cache_key(request=request, text=text)
    cached_data = await get_cached_sse_data(request=request, cache_key=cache_key)
    if cached_data:
        logger.info(f"🎯  命中SSE缓存(哈希: {cache_key[-10:]})")
        for data in cached_data:
            # 确保 data 是字节类型
            if isinstance(data, str):
                data = data.encode('utf-8')
            # 使用字节进行条件判断
            if skip_question and b'"event": "message"' in data and b'"question":' in data:
                continue
            yield data
    else:
        logger.info(f"💾  处理新的请求并进行缓存(哈希: {cache_key[-10:]})")
        buffer = []
        buffer_size = 10

        # 处理 LLM 流，确保数据是字节
        async for data in llm_server.chat_messages_streaming(request=request, text=text, skip_question=skip_question):
            # 将数据转换为字节（如果尚未是字节）
            if isinstance(data, str):
                data_bytes = data.encode('utf-8')
            else:
                data_bytes = data
            yield data_bytes
            buffer.append(data_bytes)

            if len(buffer) >= buffer_size:
                asyncio.create_task(store_sse_bulk_data(cache_key, buffer.copy()))
                buffer.clear()

        if buffer:
            await store_sse_bulk_data(cache_key, buffer)
            buffer.clear()

        # 处理 parameters 流，确保数据是字节
        param_buffer = []
        async for parameter in llm_server_other.parameters_(request=request):
            if isinstance(parameter, str):
                parameter_bytes = parameter.encode('utf-8')
            else:
                parameter_bytes = parameter
            yield parameter_bytes
            param_buffer.append(parameter_bytes)

        if param_buffer:
            await store_sse_bulk_data(cache_key, param_buffer, append=True)
            await redis_client.rpush(cache_key, b"__END_OF_STREAM__")

@router.post("/tts", description="流模式主入口", summary="通过传递音频获取全部(流模式)")
async def main_router_streaming(request: Request, text: str = Depends(stt_server.audio_to_text)):

    total_start_time = time.time() # 记录总开始时间
    stt_end_time = time.time() # STT 阶段的结束时间
    logger.info(f"⏱️  STT阶段完成，耗时: {stt_end_time - total_start_time:.3f}秒")
    logger.info(f"⏱️  识别文本: {colorama.Fore.RED} '{text}'")

    async def stream_generator():
        nonlocal total_start_time, stt_end_time
        llm_start_time = time.time() # LLM 阶段的开始时间
        first_tts_time = None # 用于记录首个 TTS URL 生成的时间
        tts_end_time = None # 用于记录 TTS 阶段的结束时间

        if text:
            question_time = time.time()
            logger.info(f"⏱️  开始发送用户问题事件，处理时间: {question_time - llm_start_time:.3f}秒")
            question_data = {"event": "message", "question": text, "status": "ready"}
            yield f"data: {orjson.dumps(question_data).decode()}\n\n".encode()
            logger.info(f"⏱️  用户问题事件已发送，耗时: {time.time() - question_time:.3f}秒")

        if text:
            cache_key = await generate_cache_key(request=request, text=text)
            cached_data = await get_cached_sse_data(request=request, cache_key=cache_key)
            if cached_data:
                logger.info(f"🎯  命中完整SSE缓存(哈希: {cache_key[-10:]})")
                for data in cached_data:
                    if isinstance(data, str):
                        data = data.encode('utf-8')
                    if b'"event": "message"' in data and b'"question":' in data:
                        continue
                    if first_tts_time is None and b'"url":' in data:
                        first_tts_time = time.time()
                        logger.info(f"⏱️  首个TTS URL生成完成，从STT完成到首个TTS耗时: {colorama.Fore.RED} {first_tts_time - stt_end_time:.3f}秒")
                    yield data
                llm_end_time = time.time() # LLM 阶段的结束时间
                tts_end_time = time.time() # 假设缓存命中时 TTS 也是瞬间完成的
                logger.info(f"⏱️  LLM缓存响应完成，耗时: {llm_end_time - llm_start_time:.3f}秒")
                logger.info("⏱️  总体处理完成，各阶段耗时统计:")
                logger.info(f"   - STT阶段: {stt_end_time - total_start_time:.3f}秒")
                logger.info(f"   - LLM阶段(缓存): {llm_end_time - llm_start_time:.3f}秒")
                if first_tts_time:
                    logger.info(f"   - STT完成到首个TTS: {first_tts_time - stt_end_time:.3f}秒")
                logger.info(f"   - 总耗时: {tts_end_time - total_start_time:.3f}秒")
                return

        logger.info("⏱️  开始生成实时LLM响应流")

        async for data in generate_stream(request=request, text=text, skip_question=True):
            # 记录第一个包含TTS URL的时间
            if first_tts_time is None and b'"url":' in data:
                first_tts_time = time.time()
                logger.info(f"⏱️  首个TTS URL生成完成，从STT完成到首个TTS耗时: {colorama.Fore.RED} {first_tts_time - stt_end_time:.3f}秒")
            yield data

    return StreamingResponse(
        stream_generator(),
        media_type='text/event-stream',
        headers={"X-Stream-Data": "true"}
    )

# --------------------------------------------------------------------------------------------------------------------
# 设备相关
@router.post("/devices", status_code=status.HTTP_201_CREATED, include_in_schema=False)
async def create_device(device_data: DeviceCreateSchema):
    try:
        device = await Device.create(**device_data.model_dump())
        return await Device_Pydantic.from_tortoise_orm(device)
    except  Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建设备失败: {str(e)}"
        )

# 获取全部设备
@router.get("/devices", summary="获取全部设备")
async def get_all_devices():
    return await Device_Pydantic.from_queryset(Device.all())


# 获取设备详情接口
@router.get("/devices/{device_id}", response_model=DeviceWithAppsSchema, summary="获取设备关联的全部应用", description="传入唯一设备ID即可")
async def get_one_device(device_id: str):
    # 使用正确的查询方式（根据name查询）
    device = await (
        Device.filter(name=device_id)
        .prefetch_related("apps")  # 预加载关联应用
        .first()
    )
    
    if not device:
        raise HTTPException(status_code=404, detail="设备不存在")
    
    # 获取设备基础信息（使用Pydantic v2语法）
    device_data = Device_Pydantic.from_orm(device)
    
    # 获取完整关联应用（包含中间表字段）
    related_apps = await device.apps.all().prefetch_related("app_devices")
    
    # 构建响应数据
    apps = [
        AppWithKeySchema(
            id=app.id,
            name=app.name,
            description=app.description,
            api_key=app.api_key,  # 应用自身的api_key
            created_at=app.created_at,
            updated_at=app.updated_at
        ) for app in related_apps
    ]
    
    return DeviceWithAppsSchema(
        **device_data.model_dump(),
        apps=apps
    )

@router.put("/devices/{device_id}", include_in_schema=False)
async def update_device(device_id:int, device_data: DeviceUpdateSchema):
    device = await Device.get_or_none(pk=device_id)
    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="设备不存在"
        )

    update_data = device_data.model_dump(exclude_unset=True)
    await device.update_from_dict(update_data).save()
    return await Device_Pydantic.from_tortoise_orm(device)

@router.get("/apps", include_in_schema=False)
async def get_all_apps():
    return await App_Pydantic.from_queryset(App.all())

