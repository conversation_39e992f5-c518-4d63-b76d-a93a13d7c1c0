from core.logger import logger
from core.services.v2 import tts_server
from settings.config import settings

async def tts_servers(*, func_name, request, text):

    logger.info(f"Using TTS function: {func_name}")

    func_dict = {
        "edge_tts": {"func": tts_server.text_to_audio_edge, "params": {"request": request, "text": text, "rate": settings.rate}}, 
        "aliyun_tts": {"func": tts_server.text_to_audio_aliyun, "params": {"request": request, "text": text}},
        "local_tts": {"func": tts_server.text_to_audio_ffmpeg_speed, "params": {"request": request, "text": text}} 
    }

    func = func_dict[func_name]['func']
    if not func:
        raise ValueError(f"Unsupported TTS function: {func_name}")
    
    return await func(**func_dict[func_name]["params"])  