# 管理系统集成说明

本项目已成功集成了 vue-fastapi-admin 的后台管理功能到您的现有项目中。

## 🎯 功能特性

- ✅ 完整的 RBAC 权限管理系统
- ✅ 用户、角色、菜单、API 权限管理  
- ✅ JWT 认证和授权
- ✅ 部门管理
- ✅ 审计日志
- ✅ 与现有项目完美集成
- ✅ 支持 MySQL 数据库
- ✅ 前端 Vue3 + Naive UI（可选）

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置数据库

确保您的 `.env` 文件中包含正确的数据库配置：

```env
# 数据库配置
db_host=localhost
db_port=3306
db_user=your_username
db_password=your_password
db_name=your_database
enable_database=true
```

### 3. 运行数据库迁移

```bash
python migrate_admin.py
```

### 4. 启动服务

```bash
python run_admin_system.py
```

## 🌐 访问地址

- **主应用**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs  
- **管理系统 API**: http://localhost:8000/api/admin/
- **原有 V2 API**: http://localhost:8000/v2/

## 🔐 默认账号

- **用户名**: admin
- **密码**: 123456

⚠️ **重要**: 请在生产环境中及时修改默认密码！

## 📚 API 端点

### 🔑 认证相关
- `POST /api/admin/auth/login` - 用户登录
- `GET /api/admin/auth/user_info` - 获取用户信息  
- `GET /api/admin/auth/menus` - 获取用户菜单

### 👥 用户管理
- `GET /api/admin/users` - 获取用户列表
- `POST /api/admin/users` - 创建用户
- `PUT /api/admin/users/{user_id}` - 更新用户
- `DELETE /api/admin/users/{user_id}` - 删除用户
- `PUT /api/admin/users/{user_id}/password` - 修改密码

### 🎭 角色管理
- `GET /api/admin/roles` - 获取角色列表
- `POST /api/admin/roles` - 创建角色
- `PUT /api/admin/roles/{role_id}` - 更新角色
- `DELETE /api/admin/roles/{role_id}` - 删除角色
- `PUT /api/admin/roles/{role_id}/permissions` - 更新角色权限

### 📋 菜单管理
- `GET /api/admin/menus` - 获取菜单列表
- `POST /api/admin/menus` - 创建菜单
- `PUT /api/admin/menus/{menu_id}` - 更新菜单
- `DELETE /api/admin/menus/{menu_id}` - 删除菜单

### 🔌 API 管理
- `GET /api/admin/apis` - 获取 API 列表
- `POST /api/admin/apis` - 创建 API
- `PUT /api/admin/apis/{api_id}` - 更新 API
- `DELETE /api/admin/apis/{api_id}` - 删除 API
- `POST /api/admin/apis/sync` - 同步 API

### 🏢 部门管理
- `GET /api/admin/depts` - 获取部门列表
- `POST /api/admin/depts` - 创建部门
- `PUT /api/admin/depts/{dept_id}` - 更新部门
- `DELETE /api/admin/depts/{dept_id}` - 删除部门

## 🎨 前端集成（可选）

如果您需要前端界面，可以使用 vue-fastapi-admin 项目中的前端代码：

### 1. 安装前端依赖

```bash
cd vue-fastapi-admin/web
npm install
```

### 2. 配置前端 API 地址

编辑 `vue-fastapi-admin/web/.env.development`：

```env
VITE_BASE_API=http://localhost:8000/api/admin
```

### 3. 启动前端

```bash
npm run dev
```

前端将在 http://localhost:3000 启动。

## 📁 项目结构

```
api_versions/admin/           # 管理系统模块
├── __init__.py
├── models.py                # 数据模型（AdminUser, AdminRole 等）
├── schemas.py               # Pydantic 模式定义
├── controllers.py           # 业务逻辑控制器
├── routers.py              # FastAPI 路由定义
├── core.py                 # 核心功能（CRUD、认证、权限等）
└── init_data.py            # 初始化数据脚本

vue-fastapi-admin/          # 原始管理系统项目
├── app/                    # 后端代码
├── web/                    # 前端代码
└── ...

# 新增文件
migrate_admin.py            # 数据库迁移脚本
run_admin_system.py         # 集成系统启动脚本
requirements.txt            # 更新的依赖列表
```

## ⚙️ 集成说明

### 数据库表设计
- 所有管理系统表都有 `admin_` 前缀，避免与现有表冲突
- 支持与现有 Tortoise ORM 配置无缝集成
- 自动创建必要的索引和关系

### 权限系统
- 基于 RBAC（基于角色的访问控制）
- 支持菜单级和 API 级权限控制
- 超级管理员拥有所有权限
- 支持动态权限分配

### 认证系统
- 使用 JWT Token 进行身份验证
- Token 有效期 7 天（可配置）
- 支持密码加密存储
- 集成到 FastAPI 依赖注入系统

## 🔧 配置说明

### JWT 配置
在 `api_versions/admin/core.py` 中可以修改：
```python
SECRET_KEY = "your-secret-key-change-this-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7 days
```

### 数据库配置
管理系统使用与主项目相同的数据库配置，在 `settings/tortoise_config.py` 中已自动添加管理模型。

## 🚨 注意事项

1. **数据库权限**: 确保数据库用户有创建表的权限
2. **表名冲突**: 管理系统表都有 `admin_` 前缀，避免冲突
3. **JWT 密钥**: 生产环境请修改默认 JWT 密钥
4. **默认密码**: 请及时修改默认管理员密码
5. **端口配置**: 如有端口冲突，请修改 `.env` 中的 `fastapi_port`

## 🛠️ 故障排除

### 数据库连接失败
```bash
# 检查数据库配置
cat .env | grep db_

# 测试数据库连接
python -c "from settings.config import settings; print(f'数据库: {settings.db_host}:{settings.db_port}/{settings.db_name}')"
```

### 权限错误
```sql
-- 确保数据库用户有足够权限
GRANT ALL PRIVILEGES ON your_database.* TO 'your_username'@'%';
FLUSH PRIVILEGES;
```

### 依赖冲突
```bash
# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

## 🎉 使用示例

### 1. 登录获取 Token
```bash
curl -X POST "http://localhost:8000/api/admin/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "123456"}'
```

### 2. 获取用户信息
```bash
curl -X GET "http://localhost:8000/api/admin/auth/user_info" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 创建新用户
```bash
curl -X POST "http://localhost:8000/api/admin/users" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "testuser",
       "email": "<EMAIL>", 
       "password": "123456",
       "alias": "测试用户"
     }'
```

## 📞 技术支持

如果您在集成过程中遇到问题，请检查：

1. 数据库连接是否正常
2. 依赖是否正确安装
3. 端口是否被占用
4. 日志中的错误信息

集成完成后，您的项目将拥有完整的后台管理功能，可以方便地管理用户、角色、权限等。
