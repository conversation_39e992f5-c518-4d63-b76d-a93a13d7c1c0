#!/usr/bin/env python3
"""
测试管理系统 API
"""
import requests
import json


def test_login():
    """测试登录 API"""
    url = "http://localhost:8001/api/admin/auth/login"
    data = {
        "username": "admin",
        "password": "123456"
    }
    
    try:
        response = requests.post(url, json=data, timeout=5)
        print(f"登录测试: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 登录成功!")
            print(f"Token: {result['data']['access_token'][:50]}...")
            return result['data']['access_token']
        else:
            print(f"❌ 登录失败: {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接失败: {e}")
        return None


def test_user_info(token):
    """测试获取用户信息"""
    url = "http://localhost:8001/api/admin/auth/user_info"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=5)
        print(f"用户信息测试: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取用户信息成功!")
            print(f"用户: {result['data']['username']}")
            print(f"邮箱: {result['data']['email']}")
            return True
        else:
            print(f"❌ 获取用户信息失败: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接失败: {e}")
        return False


def test_docs():
    """测试 API 文档是否可访问"""
    url = "http://localhost:8001/docs"
    
    try:
        response = requests.get(url, timeout=5)
        print(f"API 文档测试: {response.status_code}")
        if response.status_code == 200:
            print("✅ API 文档可访问!")
            return True
        else:
            print(f"❌ API 文档访问失败: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接失败: {e}")
        return False


def main():
    print("🧪 开始测试管理系统 API...")
    print("=" * 50)
    
    # 测试 API 文档
    print("1. 测试 API 文档访问...")
    docs_ok = test_docs()
    print("-" * 30)
    
    # 测试登录
    print("2. 测试登录 API...")
    token = test_login()
    print("-" * 30)
    
    if token:
        # 测试用户信息
        print("3. 测试用户信息 API...")
        user_info_ok = test_user_info(token)
        print("-" * 30)
    else:
        user_info_ok = False
    
    print("=" * 50)
    print("📊 测试结果:")
    print(f"   - API 文档: {'✅' if docs_ok else '❌'}")
    print(f"   - 登录功能: {'✅' if token else '❌'}")
    print(f"   - 用户信息: {'✅' if user_info_ok else '❌'}")
    
    if docs_ok and token and user_info_ok:
        print("\n🎉 所有测试通过！管理系统 API 正常运行！")
        print("\n📍 访问地址:")
        print("   - API 文档: http://localhost:8001/docs")
        print("   - 管理 API: http://localhost:8001/api/admin/")
        print("\n🔐 默认账号:")
        print("   - 用户名: admin")
        print("   - 密码: 123456")
    else:
        print("\n⚠️ 部分测试失败，请检查服务是否正常启动")


if __name__ == "__main__":
    main()
