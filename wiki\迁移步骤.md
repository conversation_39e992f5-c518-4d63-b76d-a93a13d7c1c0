## pud.env
```shell
# 注意 此处写mysql或者具体的服务器地址都可以 不过建议写mysql
DB_HOST=mysql
DB_PORT=3306
DB_USER=qtsx
DB_PASSWORD=@qtsx1688
DB_ROOT_PASSWORD=xm123
DB_NAME=tts_db
DB_EXPOSE_PORT=3306
```

## dev.env

```shell
DB_HOST=localhost
DB_PORT=3306
DB_USER=qtsx
DB_PASSWORD=@qtsx1688
DB_ROOT_PASSWORD=xm123
DB_NAME=tts_db
DB_EXPOSE_PORT=3306
```

## windows服务器
```shell

mysqldump -uroot -p1314520sm tts_db > backup.sql
```

## linux服务器
```shell
docker compose up -d --build
docker cp backup.sql mysql:/tmp/backup.sql

docker exec -it mysql bash
mysql -u qtsx -p tts_db < /tmp/backup.sql
exit

```

## 非常重要！
```shell
docker compose down  # 该命令不会删除数据库的数据
docker compose down -v # 该命令会删除数据库的数据

# 如果程序已经通过上述步骤正常启动了， 那么在执行 docker compose down 之后 应该执行的命令是 docker compose up -d 而不是docker compose up -d --build

```

---
## 创建用户赋予权限(题外话)
```shell
mysql -u root -p
CREATE USER 'newuser'@'%' IDENTIFIED BY 'password';

# 增删改查
GRANT ALL PRIVILEGES ON *.* TO 'newuser'@'%' WITH GRANT OPTION;

# 导出
GRANT PROCESS ON *.* TO 'qtsx'@'%';
FLUSH PRIVILEGES;
```