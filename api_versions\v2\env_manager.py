import os
import sys
import subprocess
import pkg_resources
from typing import List, Dict, Any
from settings.config import settings

class EnvManager:
    """虚拟环境管理类"""
    
    @staticmethod
    def get_env_info() -> Dict[str, Any]:
        """获取当前虚拟环境信息"""
        env_info = {
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "env_path": os.environ.get("VIRTUAL_ENV", "未激活虚拟环境"),
            "system_info": {
                "os": os.name,
                "platform": sys.platform
            },
            "packages": []
        }
        
        # 获取已安装的包
        try:
            installed_packages = pkg_resources.working_set
            env_info["packages"] = [
                {"name": i.key, "version": i.version}
                for i in installed_packages
            ]
        except Exception as e:
            print(f"获取包信息失败: {e}")
        
        # 获取环境变量信息
        env_info["env_vars"] = {
            "BASE_URL": settings.base_url,
            "TTS_SERVICE": settings.tts_service,
            "FASTAPI_PORT": settings.fastapi_port,
            "LOG_LEVEL": settings.log_level,
            "REDIS_HOST": settings.redis_host,
            "REDIS_PORT": settings.redis_port
        }
        
        return env_info
    
    @staticmethod
    async def restart_service() -> Dict[str, str]:
        """重启服务"""
        try:
            # 在Windows上使用不同的重启命令
            if sys.platform == "win32":
                # 使用异步子进程执行命令
                subprocess.Popen(["python", "app.py"], 
                                 cwd=os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            else:
                # Linux/Mac上使用不同的命令
                subprocess.Popen(["pkill", "-f", "app.py"])
                subprocess.Popen(["python", "app.py"], 
                                 cwd=os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            
            return {"status": "success", "message": "服务重启指令已发送"}
        except Exception as e:
            return {"status": "error", "message": f"重启服务失败: {str(e)}"}
    
    @staticmethod
    async def update_env_var(var_name: str, var_value: str) -> Dict[str, str]:
        """更新环境变量"""
        try:
            # 读取.env文件
            env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), ".env")
            
            if not os.path.exists(env_path):
                return {"status": "error", "message": ".env文件不存在"}
            
            with open(env_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
            
            # 查找并更新变量
            updated = False
            for i, line in enumerate(lines):
                if line.strip().startswith(f"{var_name}="):
                    lines[i] = f"{var_name}={var_value}\n"
                    updated = True
                    break
            
            # 如果变量不存在，则添加
            if not updated:
                lines.append(f"{var_name}={var_value}\n")
            
            # 写回文件
            with open(env_path, "w", encoding="utf-8") as f:
                f.writelines(lines)
            
            return {"status": "success", "message": f"环境变量 {var_name} 已更新"}
        except Exception as e:
            return {"status": "error", "message": f"更新环境变量失败: {str(e)}"}

env_manager = EnvManager()