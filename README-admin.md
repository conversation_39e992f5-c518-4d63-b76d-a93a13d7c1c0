# 数字人管理系统

本项目整合了vue-fastapi-admin前端和现有v2接口后端，提供了一个完整的数字人管理系统。

## 功能特点

- 设备管理：查看和管理数字人设备
- 虚拟环境管理：查看和控制Python虚拟环境
- 环境变量管理：在线修改.env配置文件
- 系统管理：用户、角色、菜单等管理功能

## 快速开始

### 启动系统

```bash
# 启动整合系统（同时启动后端和前端）
python start_admin.py
```

### 访问系统

- 前端地址：http://localhost:3100
- 后端API地址：http://localhost:9999

### 登录信息

- 用户名：admin
- 密码：123456

## 目录结构

```
human_api_dev_latest/
├── api_versions/            # API版本目录
│   ├── v2/                  # V2版本API
│   │   ├── admin_routers.py # 管理接口路由
│   │   ├── env_manager.py   # 虚拟环境管理类
│   │   └── routers.py       # V2接口路由
├── vue-fastapi-admin/       # 前端项目
│   ├── web/                 # 前端代码
│   │   ├── src/             # 源代码
│   │   │   ├── views/       # 页面组件
│   │   │   │   └── digital-human/ # 数字人管理页面
├── .env                     # 环境变量配置
├── app.py                   # 主应用入口
└── start_admin.py           # 启动脚本
```

## 虚拟环境管理

通过系统的"虚拟环境"页面，您可以：

1. 查看Python版本和虚拟环境路径
2. 查看已安装的Python包
3. 查看和修改环境变量
4. 重启服务
5. 更新依赖

## 设备管理

通过系统的"设备管理"页面，您可以：

1. 查看所有设备列表
2. 添加新设备
3. 编辑现有设备
4. 删除设备

## 注意事项

- 修改环境变量后，需要重启服务才能生效
- 系统使用SQLite数据库，数据存储在db.sqlite3文件中