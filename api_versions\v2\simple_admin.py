from fastapi import APIRouter, Depends, HTTPException, status, Request, Body
from fastapi.security import <PERSON>Auth2P<PERSON>word<PERSON>earer, OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import jwt
from datetime import datetime, timedelta
import os
import sys

# 创建路由器
router = APIRouter()

# 定义模型
class Token(BaseModel):
    access_token: str
    token_type: str

class UserInfo(BaseModel):
    id: int
    username: str
    email: str
    is_active: bool = True
    is_superuser: bool = False
    avatar: Optional[str] = None

# 创建一个密钥
SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7天

# 模拟用户数据
USERS = {
    "admin": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "password": "123456",
        "is_active": True,
        "is_superuser": True,
        "avatar": None
    }
}

# 创建token
def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# 登录接口
@router.post("/base/access_token")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    print(f"Login attempt: {form_data.username}")
    user = USERS.get(form_data.username)
    if not user or form_data.password != user["password"]:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# 获取用户信息
@router.get("/base/userinfo")
async def get_user_info(request: Request):
    print("Getting user info")
    # 这里应该从token中获取用户信息，但为了简化，我们直接返回admin用户
    user = USERS["admin"]
    return UserInfo(
        id=user["id"],
        username=user["username"],
        email=user["email"],
        is_active=user["is_active"],
        is_superuser=user["is_superuser"],
        avatar=user["avatar"]
    )

# 获取用户菜单
@router.get("/base/usermenu")
async def get_user_menu():
    print("Getting user menu")
    # 返回一些示例菜单
    return [
        {
            "id": 1,
            "name": "系统管理",
            "path": "/system",
            "component": "Layout",
            "redirect": "/system/user",
            "meta": {
                "title": "系统管理",
                "icon": "carbon:gui-management",
                "order": 1,
                "hideMenu": False,
            },
            "children": [
                {
                    "id": 2,
                    "name": "用户管理",
                    "path": "user",
                    "component": "/system/user",
                    "meta": {
                        "title": "用户管理",
                        "icon": "material-symbols:person-outline-rounded",
                        "order": 1,
                        "hideMenu": False,
                    }
                },
                {
                    "id": 3,
                    "name": "角色管理",
                    "path": "role",
                    "component": "/system/role",
                    "meta": {
                        "title": "角色管理",
                        "icon": "carbon:user-role",
                        "order": 2,
                        "hideMenu": False,
                    }
                }
            ]
        }
    ]

# 获取用户API权限
@router.get("/base/userapi")
async def get_user_api():
    print("Getting user API")
    # 返回用户可访问的API列表
    return [
        "/base/userinfo",
        "/base/usermenu",
        "/base/userapi"
    ]