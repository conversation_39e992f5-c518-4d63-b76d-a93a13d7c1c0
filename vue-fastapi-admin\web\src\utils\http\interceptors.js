import { getToken } from '@/utils/auth'
import { useUserStore } from '@/store'
import { isNullOrUndef } from '@/utils'

export function reqResolve(config) {
  // 防止缓存，给get请求加上时间戳
  if (config.method === 'get') {
    config.params = { ...config.params, t: new Date().getTime() }
  }

  // 处理不需要token的请求
  if (config.noNeedToken) {
    return config
  }

  const token = getToken()
  if (!token) {
    return config
  }

  /**
   * * jwt token
   * ! 认证方案: Bearer
   */
  config.headers.Authorization = config.headers.Authorization || 'Bearer ' + token

  return config
}

export function reqReject(error) {
  return Promise.reject(error)
}

export function resResolve(response) {
  console.log('Response:', response)
  return response.data
}

export function resReject(error) {
  console.error('Request error:', error)
  
  // 处理401错误
  if (error.response?.status === 401) {
    const userStore = useUserStore()
    userStore.logout()
    window.$message?.error('登录已过期，请重新登录')
    return Promise.reject(error)
  }
  
  // 处理其他错误
  const message = error.response?.data?.detail || error.message || '服务器错误'
  window.$message?.error(message)
  return Promise.reject(error)
}