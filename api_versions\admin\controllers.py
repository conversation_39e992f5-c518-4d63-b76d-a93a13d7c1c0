from typing import List, Optional, Dict, Any
from tortoise.queryset import Q
from .core import CR<PERSON><PERSON><PERSON><PERSON>, PasswordUtils, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TreeBuilder
from .models import Admin<PERSON>ser, AdminRole, AdminMenu, AdminApi, AdminDept, AdminAuditLog
from .schemas import (
    UserCreate, UserUpdate, RoleCreate, RoleUpdate, 
    MenuCreate, MenuUpdate, ApiCreate, ApiUpdate,
    DeptCreate, DeptUpdate, LoginForm
)


class UserController(CRUDBase[AdminUser, UserCreate, UserUpdate]):
    def __init__(self):
        super().__init__(model=AdminUser)

    async def create_user(self, user_create: UserCreate) -> AdminUser:
        # 检查用户名和邮箱是否已存在
        if await self.model.filter(username=user_create.username).exists():
            raise ValueError("用户名已存在")
        if await self.model.filter(email=user_create.email).exists():
            raise ValueError("邮箱已存在")
        
        # 创建用户数据
        user_data = user_create.create_dict()
        user_data["password"] = PasswordUtils.get_password_hash(user_create.password)
        
        # 创建用户
        user = await self.model.create(**user_data)
        
        # 分配角色
        if user_create.role_ids:
            roles = await AdminRole.filter(id__in=user_create.role_ids)
            await user.roles.add(*roles)
        
        return user

    async def authenticate(self, username: str, password: str) -> Optional[AdminUser]:
        user = await self.model.filter(username=username, is_active=True).first()
        if not user:
            return None
        if not PasswordUtils.verify_password(password, user.password):
            return None
        
        # 更新最后登录时间
        from datetime import datetime
        user.last_login = datetime.now()
        await user.save()
        
        return user

    async def update_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        user = await self.get(user_id)
        if not user:
            return False
        
        if not PasswordUtils.verify_password(old_password, user.password):
            return False
        
        user.password = PasswordUtils.get_password_hash(new_password)
        await user.save()
        return True

    async def get_user_with_roles(self, user_id: int) -> Optional[AdminUser]:
        return await self.model.filter(id=user_id).prefetch_related("roles").first()

    async def update_user_roles(self, user_id: int, role_ids: List[int]) -> AdminUser:
        user = await self.get(user_id)
        if not user:
            raise ValueError("用户不存在")
        
        await user.roles.clear()
        if role_ids:
            roles = await AdminRole.filter(id__in=role_ids)
            await user.roles.add(*roles)
        
        return user


class RoleController(CRUDBase[AdminRole, RoleCreate, RoleUpdate]):
    def __init__(self):
        super().__init__(model=AdminRole)

    async def is_exist(self, name: str) -> bool:
        return await self.model.filter(name=name).exists()

    async def update_role_permissions(
        self, 
        role_id: int, 
        menu_ids: List[int], 
        api_infos: List[dict]
    ) -> AdminRole:
        role = await self.get(role_id)
        if not role:
            raise ValueError("角色不存在")
        
        # 更新菜单权限
        await role.menus.clear()
        if menu_ids:
            menus = await AdminMenu.filter(id__in=menu_ids)
            await role.menus.add(*menus)
        
        # 更新API权限
        await role.apis.clear()
        for api_info in api_infos:
            api = await AdminApi.filter(
                path=api_info.get("path"), 
                method=api_info.get("method")
            ).first()
            if api:
                await role.apis.add(api)
        
        return role

    async def get_role_with_permissions(self, role_id: int) -> Optional[AdminRole]:
        return await self.model.filter(id=role_id).prefetch_related("menus", "apis").first()


class MenuController(CRUDBase[AdminMenu, MenuCreate, MenuUpdate]):
    def __init__(self):
        super().__init__(model=AdminMenu)

    async def get_menu_tree(self) -> List[Dict]:
        menus = await self.model.all().order_by("order", "id")
        menu_list = []
        for menu in menus:
            menu_dict = {
                "id": menu.id,
                "name": menu.name,
                "path": menu.path,
                "component": menu.component,
                "icon": menu.icon,
                "order": menu.order,
                "parent_id": menu.parent_id,
                "is_hidden": menu.is_hidden,
                "menu_type": menu.menu_type,
                "keepalive": menu.keepalive,
                "redirect": menu.redirect,
                "remark": menu.remark
            }
            menu_list.append(menu_dict)
        
        return TreeBuilder.build_tree(menu_list)

    async def get_user_menus(self, user_id: int) -> List[Dict]:
        user = await AdminUser.filter(id=user_id).prefetch_related("roles__menus").first()
        if not user:
            return []
        
        menu_ids = set()
        if user.is_superuser:
            # 超级用户获取所有菜单
            menus = await self.model.all()
            menu_ids = {menu.id for menu in menus}
        else:
            # 普通用户根据角色获取菜单
            for role in user.roles:
                for menu in role.menus:
                    menu_ids.add(menu.id)
        
        if not menu_ids:
            return []
        
        menus = await self.model.filter(id__in=menu_ids).order_by("order", "id")
        menu_list = []
        for menu in menus:
            menu_dict = {
                "id": menu.id,
                "name": menu.name,
                "path": menu.path,
                "component": menu.component,
                "icon": menu.icon,
                "order": menu.order,
                "parent_id": menu.parent_id,
                "is_hidden": menu.is_hidden,
                "menu_type": menu.menu_type,
                "keepalive": menu.keepalive,
                "redirect": menu.redirect,
                "meta": {
                    "title": menu.name,
                    "icon": menu.icon,
                    "hidden": menu.is_hidden,
                    "keepAlive": menu.keepalive
                }
            }
            menu_list.append(menu_dict)
        
        return TreeBuilder.build_tree(menu_list)


class ApiController(CRUDBase[AdminApi, ApiCreate, ApiUpdate]):
    def __init__(self):
        super().__init__(model=AdminApi)

    async def sync_apis_from_openapi(self, openapi_data: Dict) -> int:
        """从OpenAPI数据同步API信息"""
        synced_count = 0
        paths = openapi_data.get("paths", {})
        
        for path, methods in paths.items():
            for method, details in methods.items():
                if method.upper() in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                    summary = details.get("summary", "")
                    tags = ",".join(details.get("tags", []))
                    
                    # 检查API是否已存在
                    existing_api = await self.model.filter(
                        path=path, 
                        method=method.upper()
                    ).first()
                    
                    if not existing_api:
                        await self.model.create(
                            path=path,
                            method=method.upper(),
                            summary=summary,
                            tags=tags
                        )
                        synced_count += 1
        
        return synced_count


class DeptController(CRUDBase[AdminDept, DeptCreate, DeptUpdate]):
    def __init__(self):
        super().__init__(model=AdminDept)

    async def get_dept_tree(self) -> List[Dict]:
        depts = await self.model.filter(is_deleted=False).order_by("order", "id")
        dept_list = []
        for dept in depts:
            dept_dict = {
                "id": dept.id,
                "name": dept.name,
                "desc": dept.desc,
                "order": dept.order,
                "parent_id": dept.parent_id,
                "created_at": dept.created_at,
                "updated_at": dept.updated_at
            }
            dept_list.append(dept_dict)
        
        return TreeBuilder.build_tree(dept_list)


class AuthController:
    def __init__(self):
        self.user_controller = UserController()

    async def login(self, login_form: LoginForm) -> Dict[str, Any]:
        user = await self.user_controller.authenticate(
            login_form.username, 
            login_form.password
        )
        if not user:
            raise ValueError("用户名或密码错误")
        
        # 创建访问令牌
        access_token = JWTUtils.create_access_token(
            data={"sub": str(user.id), "username": user.username}
        )
        
        # 获取用户权限
        permissions = await PermissionChecker.get_user_permissions(user.id)
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user_info": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "alias": user.alias,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "permissions": permissions
            }
        }

    async def get_current_user(self, token: str) -> Optional[AdminUser]:
        payload = JWTUtils.verify_token(token)
        if not payload:
            return None
        
        user_id = payload.get("sub")
        if not user_id:
            return None
        
        return await self.user_controller.get(int(user_id))


# 创建控制器实例
user_controller = UserController()
role_controller = RoleController()
menu_controller = MenuController()
api_controller = ApiController()
dept_controller = DeptController()
auth_controller = AuthController()
