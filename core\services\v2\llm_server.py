import re
import os
import aiofiles
import asyncio
import aiohttp
import random
import orj<PERSON>
import asyncio
import thulac
from urllib.parse import urlparse
from settings.config import AUDIO_DIR
from core.logger import logger
from core.dependencies import urls, get_headers
from settings.config import TEXT_LIST
from core.decorators.async_tools import async_timer
from utils.tools import remove_emojis
from core.services.v2 import tts_server, llm_server_other
from core.redis_client import redis_client
from settings.config import TEXT_LIST, settings
from utils.llm_tools import get_tag_url
from utils.tools import normalize_text_numbers, cut_words, greeting, clean_text_for_tts
from utils.tts_tools import tts_servers



symbol_tuple = ('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.mp3', '.wav', '.aac', '.ogg', '.mp4', '.avi', '.mov', '.mkv')

IMAGE_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'}
AUDIO_EXTENSIONS = {'.mp3', '.wav', '.aac', '.ogg'}
VIDEO_EXTENSIONS = {'.mp4', '.avi', '.mov', '.mkv'}

@async_timer
async def chat_messages_block(*, request, text, **kwargs):
    headers = get_headers(request).copy()
    user_id = kwargs.get('user_id') or request.state.user_id
    data = {
        "inputs": {},
        "query": text,
        "response_mode": "blocking",
        "conversation_id": "",
        "user": user_id,
    }
    async with aiohttp.ClientSession() as session:
        async with session.post(url=urls['chat-messages'], headers=headers, json=data) as resp:
            result_json = await resp.json()
            random_text = random.choice(TEXT_LIST)
            return result_json.get("answer", random_text)
        
# @async_timer
async def chat_messages_streaming(*, request, text, **kwargs):
    # 使用信号量限制并发数量
    # semaphore = asyncio.Semaphore(10)  # 允许最多 10 个并发请求
    # async with semaphore:
    if text:
        question = text
        headers = get_headers(request).copy()
        api_key = request.state.api_key or settings.api_key
        user_id = kwargs.get('user_id') or request.state.user_id
        redis_key = f"conn:{api_key}:{user_id}"
        next_suggested_key = f"suggested:{api_key}:{user_id}"
        # 获取上一次会话ID 不存在时候返回空字符串
        old_conv_id = await redis_client.get(redis_key) or ""

        logger.debug(f"{__name__} {redis_key} {next_suggested_key} {old_conv_id}")

        data = {
            "inputs": {},
            "query": text,
            "response_mode": "streaming",
            "conversation_id": old_conv_id,
            "user": user_id,
        }


        if settings.greeting_enable:
            async for greeting_data in greeting(request):
                yield greeting_data     


        async with aiohttp.ClientSession() as session:
            async with session.post(url=urls['chat-messages'], headers=headers, json=data) as resp:

                skip_question = kwargs.get('skip_question', False)
                if not skip_question:
                    question_data = {"event": "message","question": question, "status": "ready"}
                    bytes_data = orjson.dumps(question_data)
                    sse_message = f"data: {bytes_data.decode()}\n\n".encode()
                    yield sse_message
                
                try:
                    text_chunk = ""
                    foobar_text = ""


                    link_buffer = []
                    is_collecting_link = False
                    collected_links = []


                    next_suggested_question = {}
                    async for chunk in resp.content:
                        if chunk.startswith(b"data:"):
                            orjson_data = orjson.loads(chunk[6:])
                            logger.debug(f"{__name__} orjson_data:{orjson_data}")
                            if orjson_data.get('event') == "message":
                                base_answer = orjson_data.get('answer')

                                # await asyncio.create_task(write_text(content=base_answer, question=question))

                                base_answer = base_answer.replace(r"<think>", "").replace(r"</think>", "")


                                # 检测是否开始或正在收集链接
                                stripped = base_answer.strip()

                                # 最终稳定版：字面检测+增强正则
                                if not is_collecting_link and (
                                    any(kw in stripped.lower() for kw in ["http", "https", "![", "["])
                                    or re.search(r'(!\[[*]?|\[)', stripped)
                                ):
                                    logger.debug("开始收集链接", stripped)
                                    is_collecting_link = True
                                    link_buffer = [stripped]
                                    continue

                                elif is_collecting_link:
                                    # 收集中
                                    link_buffer.append(stripped)
                                    # 结束条件
                                    if ")" in stripped:
                                        full_link = "".join(link_buffer)
                                        collected_links.append(full_link)
                                        link_buffer = []
                                        is_collecting_link = False
                                    
                                    # 链接片段不播报
                                    continue

                                # logger.info(collected_links)
                                answer = base_answer.replace("*", "")\
                                                    .replace("#", "")\
                                                    .replace("-", " ")\
                                                    .replace("_", " ")\
                                                    .replace(".", " ")\
                                                    .replace("[", " ")\
                                                    .replace("]", " ")\
                                                    .replace("!", " ")
                                                    
                                # 不填参数有bug 英文模式
                                text_chunk += answer.strip()
                                foobar_text += base_answer

                                if text_chunk.endswith(tuple(settings.symbols)) and len(text_chunk) >= settings.cut_length:

                                    text = await remove_emojis(text_chunk)
                                    # 数字转中文
                                    text = normalize_text_numbers(text) if settings.numbers_to_chinese else text

                                    tts_url = await tts_servers(func_name=settings.tts_service, request=request, text=text)
                                    
                                    event_data =  {"event": "message", "answer": foobar_text, "status": "ok", "url": tts_url}
                                    bytes_data = orjson.dumps(event_data)
                                    sse_message = f"data: {bytes_data.decode()}\n\n".encode()
                                    yield sse_message
                                    text_chunk = ""
                                    foobar_text = ""

                            elif orjson_data.get("event") == "message_end":
                                message_id = orjson_data.get("message_id")
                                logger.debug(f"用户: {user_id} 更新message_id: {message_id}")
                                await asyncio.create_task(redis_client.setex(
                                    name=next_suggested_key,
                                    time=settings.cache_expiry,
                                    value=message_id
                                ))
                                next_suggested = await llm_server_other.get_next_suggested(
                                    request=request,
                                    user_id=user_id,
                                    suggested_redis_key=next_suggested_key
                                )
                                next_suggested_question['data'] = next_suggested['data']

                                new_conversation_id = orjson_data.get("conversation_id")
                                logger.debug(f"{__name__} 用户：{user_id} 更新会话id  为：{new_conversation_id}")
                                asyncio.create_task(redis_client.setex(
                                    name=redis_key,
                                    time=settings.cache_expiry,
                                    value=new_conversation_id
                                ))
                    # 处理剩余文本
                    if text_chunk:
                        if text_clean := await remove_emojis(text_chunk):
                            text_clean = normalize_text_numbers(text_clean) 
                            tts_url = await tts_servers(func_name=settings.tts_service, request=request, text=text_clean)

                            event_data = {"event": "message", "answer": foobar_text, "status": "ok", "url": tts_url}
                            sse_msg = f"data: {orjson.dumps(event_data).decode()}\n\n".encode()
                            yield sse_msg

                    for link in collected_links:
                        # 如果是 markdown 格式的图片
                        # if link.startswith("!["):
                        #     title, link_url = get_tag_url(link).values()
                        # else:
                        #     # 否则直接提取链接
                        #     title, link_url = "", link

                        # if not link_url:
                        #     continue


                        title, link_url = get_tag_url(link).values()

                        # 提取扩展名
                        # path = urlparse(link_url).path
                        # ext = os.path.splitext(path)[-1].lower()

                        logger.debug(f"{title} {link_url}")

                        ext = "." + link_url.rsplit('.')[-1]

                        logger.debug(f"{__name__} 用户：{user_id} 获取链接：{link_url}  后缀是: {ext}")

                        if ext in IMAGE_EXTENSIONS:
                            event_type = "image_link"
                        elif ext in AUDIO_EXTENSIONS:
                            event_type = "audio_link"
                        elif ext in VIDEO_EXTENSIONS:
                            event_type = "video_link"
                        else:
                            event_type = "generic_link"  # 其他未分类链接

                        event_data = {
                            "event": event_type,
                            "link_data": {
                                "title": title,
                                "url": link_url
                            },
                            "status": "ok"
                        }
                        
                        bytes_data = orjson.dumps(event_data)
                        sse_data = f"data: {bytes_data.decode()}\n\n".encode()
                        yield sse_data
                    
                    # 后续建议问题
                    bytes_data = orjson.dumps(next_suggested_question)
                    sse_message = f"data: {bytes_data.decode()}\n\n".encode()
                    yield sse_message

                except Exception as e:
                    import traceback
                    traceback.print_exc()
                    err_data = {"event": "error", "detail": str(e), "answer": random.choice(TEXT_LIST)}
                    bytes_data = orjson.dumps(err_data)
                    sse_message = f"data: {bytes_data.decode()}\n\n".encode()
                    yield sse_message
    else:
        # 随机回复
        yield b'data: {"event": "message","question": ""}\n\n'

        text = random.choice(TEXT_LIST)
        tts_url = await tts_servers(func_name=settings.tts_service, request=request, text=text)   

        event_data =  {"event": "message", "status": "error", "answer": text, "url": tts_url}
        bytes_data = orjson.dumps(event_data)
        sse_message = f"data: {bytes_data.decode()}\n\n".encode()
        yield sse_message
    
async def write_text(*, content, question):
    filename = f"{question}.txt"
    async with aiofiles.open(AUDIO_DIR / filename, "a", encoding="utf-8") as f:
        await f.write(content.strip() + "\n")
        await f.flush()
    

# websocket流式接口
async def chat_messages_streaming_ws(*, request, text:str):
    async for chunk in chat_messages_streaming(request=request, text=text):
        yield chunk