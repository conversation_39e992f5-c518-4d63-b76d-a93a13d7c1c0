services:
  app:
    container_name: human_api_v4
    build: .
    restart: always
    ports:
      - "${EXPOSE_PORT:-8288}:${FASTAPI_PORT:-8000}"
    volumes:
      - .:/app
      - audio_cache:/app/audio_cache
    env_file:
      - .env
    environment:
      - REDIS_HOST=redis
      - DB_HOST=mysql # 新增：指定数据库主机为 docker-compose 中的 mysql 服务
    depends_on:
      - redis
      - mysql # 新增mysql依赖服务
    networks:
      - app_network

  redis:
    image: redis:alpine
    restart: always
    container_name: redis-v4
    command: redis-server --requirepass ${REDIS_PASSWORD} --bind 0.0.0.0
    ports:
      - "${REDIS_PORT:-6388}:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network
  
  mysql: # 新增 MySQL 服务
    image: mysql:8.0 # 您可以根据需要选择 MySQL 版本
    container_name: mysql-v4
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD} # 从 .env 文件读取 root 密码
      MYSQL_DATABASE: ${DB_NAME} # 从 .env 文件读取数据库名
      MYSQL_USER: ${DB_USER} # 从 .env 文件读取数据库用户名
      MYSQL_PASSWORD: ${DB_PASSWORD} # 从 .env 文件读取数据库用户密码
    ports:
      - "${DB_EXPOSE_PORT:-3307}:3306" # 将容器的 3306 端口映射到主机的指定端口
    volumes:
      - mysql_data:/var/lib/mysql # 持久化数据库数据
    networks:
      - app_network

volumes:
  redis_data:
  audio_cache:
  mysql_data:

networks:
  app_network:
    driver: bridge