# 管理系统 SQLite3 快速启动指南

已将数据库配置修改为 SQLite3，无需复杂的数据库配置，开箱即用！

## 🚀 一键启动

```bash
python quick_start.py
```

这个脚本会自动：
1. 创建 SQLite3 数据库文件 (`db.sqlite3`)
2. 生成所有必要的数据表
3. 初始化管理员账号和基础数据
4. 启动服务器

## 🌐 访问地址

启动成功后，您可以访问：

- **主应用**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **管理系统 API**: http://localhost:8000/api/admin/

## 🔐 默认账号

- **用户名**: admin
- **密码**: 123456

## 🎨 前端启动（可选）

如果您需要前端界面：

```bash
cd vue-fastapi-admin/web
npm install
npm run dev
```

前端将在 http://localhost:3100 启动。

## 📊 测试集成

运行测试脚本验证集成是否成功：

```bash
python test_admin_integration.py
```

## 🔧 配置说明

### 数据库文件
- SQLite3 数据库文件：`db.sqlite3`
- 位置：项目根目录
- 无需额外配置，自动创建

### 环境变量
系统会自动设置 `USE_SQLITE=true`，强制使用 SQLite3 配置。

### 前端配置
前端 API 地址已配置为：`http://localhost:8000/api/admin`

## 📚 API 使用示例

### 1. 登录获取 Token

```bash
curl -X POST "http://localhost:8000/api/admin/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "123456"}'
```

响应示例：
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "user_info": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "is_superuser": true
    }
  }
}
```

### 2. 获取用户信息

```bash
curl -X GET "http://localhost:8000/api/admin/auth/user_info" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 获取用户列表

```bash
curl -X GET "http://localhost:8000/api/admin/users" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 创建新用户

```bash
curl -X POST "http://localhost:8000/api/admin/users" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "testuser",
       "email": "<EMAIL>",
       "password": "123456",
       "alias": "测试用户"
     }'
```

## 🗂️ 数据库表结构

SQLite3 数据库包含以下管理系统表：

- `admin_user` - 管理员用户
- `admin_role` - 角色
- `admin_menu` - 菜单
- `admin_api` - API 权限
- `admin_dept` - 部门
- `admin_audit_log` - 审计日志
- 关联表（用户-角色、角色-菜单、角色-API）

## 🛠️ 故障排除

### 端口被占用
如果 8000 端口被占用，修改 `.env` 文件中的 `fastapi_port`：

```env
fastapi_port=8001
```

### 数据库文件权限
确保项目目录有写入权限，SQLite3 需要创建数据库文件。

### 依赖问题
重新安装依赖：

```bash
pip install -r requirements.txt
```

### 重置数据库
删除 `db.sqlite3` 文件，重新运行 `python quick_start.py`。

## 🎯 功能特性

- ✅ 完整的 RBAC 权限管理
- ✅ 用户、角色、菜单、API 管理
- ✅ JWT 认证
- ✅ 部门管理
- ✅ SQLite3 数据库（无需配置）
- ✅ 自动初始化数据
- ✅ 前端 Vue3 + Naive UI

## 📞 技术支持

如果遇到问题：

1. 检查 Python 版本（推荐 3.8+）
2. 确保所有依赖正确安装
3. 检查项目目录权限
4. 查看控制台错误信息

现在您可以轻松使用完整的管理系统功能了！
