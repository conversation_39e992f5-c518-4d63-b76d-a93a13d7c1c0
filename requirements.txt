# Core dependencies from your existing project
aerich==0.9.0
aiofiles==24.1.0
aiohappyeyeballs==2.4.6
aiohttp==3.11.13
aiomysql==0.2.0
aiosignal==1.3.2
aiosqlite==0.21.0
aiostream==0.6.4
annotated-types==0.7.0
anyio==4.8.0
async-timeout==5.0.1
asyncclick==8.1.8.0
attrs==25.1.0
blis==1.3.0
catalogue==2.0.10
certifi==2025.4.26
charset-normalizer==3.4.2
click==8.1.8
cloudpathlib==0.21.1
cn2an==0.5.23
colorama==0.4.6
confection==0.1.5
cymem==2.0.11
dictdiffer==0.9.0
distro==1.9.0
edge-tts==7.0.2
emoji==2.14.1
fake-useragent==2.2.0
fastapi==0.115.10
frozenlist==1.5.0
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
idna==3.10
iso8601==2.1.0
jieba==0.42.1
Jinja2==3.1.6
jiter==0.9.0
joblib==1.5.1
langcodes==3.5.0
language_data==1.3.0
loguru==0.7.3
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
multidict==6.1.0
murmurhash==1.0.13
nltk==3.9.1
numpy==2.2.6
openai==1.78.1
orjson==3.10.18
packaging==25.0
preshed==3.0.9
proces==0.1.7
propcache==0.3.0
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
pydub==0.25.1
Pygments==2.19.1
PyJWT==2.10.1
PyMySQL==1.1.1
pypika-tortoise==0.5.0
python-dotenv==1.0.1
python-multipart==0.0.20
pytz==2025.2
redis==5.2.1
regex==2024.11.6
requests==2.32.3
rich==14.0.0
shellingham==1.5.4
smart-open==7.1.0
sniffio==1.3.1
spacy==3.8.7
spacy-legacy==3.0.12
spacy-loggers==1.0.5
spacy_pkuseg==1.0.0
srsly==2.5.1
srt==3.5.3
starlette==0.46.0
tabulate==0.9.0
thinc==8.3.6
thulac==0.2.2
tomlkit==0.13.2
tortoise-orm==0.25.0
tqdm==4.67.1
typer==0.15.4
typing_extensions==4.12.2
ujson==5.10.0
urllib3==2.4.0
uvicorn==0.34.0
wasabi==1.1.3
weasel==0.4.1
win32_setctime==1.2.0
wrapt==1.17.2
yarl==1.18.3
cryptography==45.0.3
librosa==0.11.0
soundfile==0.13.1
dashscope==1.23.4

# Admin dependencies from vue-fastapi-admin
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
black==24.10.0
cffi==1.17.1
dnspython==2.7.0
email-validator==2.2.0
fastapi-cli==0.0.7
httptools==0.6.4
isort==5.13.2
mypy-extensions==1.0.0
passlib==1.7.4
pathspec==0.12.1
platformdirs==4.3.6
pycparser==2.22
pyyaml==6.0.2
rich-toolkit==0.13.2
ruff==0.9.1
setuptools==75.8.0
uvloop==0.21.0; sys_platform != 'win32'
watchfiles==1.0.4
websockets==14.1
