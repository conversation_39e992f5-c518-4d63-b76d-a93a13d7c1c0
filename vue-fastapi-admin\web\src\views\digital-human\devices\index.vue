<template>
  <common-page show-footer title="设备管理">
    <crud-table
      :columns="columns"
      :get-list-func="getDeviceList"
      :delete-func="deleteDevice"
      :create-func="createDevice"
      :update-func="updateDevice"
    >
      <template #modalForm="{ form }">
        <n-form-item label="设备名称" path="name">
          <n-input v-model:value="form.name" placeholder="请输入设备名称" />
        </n-form-item>
        <n-form-item label="设备描述" path="description">
          <n-input v-model:value="form.description" placeholder="请输入设备描述" />
        </n-form-item>
        <n-form-item label="设备状态" path="status">
          <n-select
            v-model:value="form.status"
            :options="[
              { label: '活跃', value: 'active' },
              { label: '非活跃', value: 'inactive' },
            ]"
            placeholder="请选择设备状态"
          />
        </n-form-item>
      </template>
    </crud-table>
  </common-page>
</template>

<script setup>
import { h } from 'vue'
import { NTag, NButton, NSpace } from 'naive-ui'
import { useCRUD } from '@/composables'

const { getDeviceList, createDevice, updateDevice, deleteDevice } = useCRUD({
  baseUrl: '/device',
  dataPath: 'items',
})

const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
  },
  {
    title: '设备名称',
    key: 'name',
    width: 150,
  },
  {
    title: '设备描述',
    key: 'description',
    width: 200,
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      return h(
        NTag,
        {
          type: row.status === 'active' ? 'success' : 'warning',
        },
        {
          default: () => (row.status === 'active' ? '活跃' : '非活跃'),
        }
      )
    },
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
  },
]
</script>