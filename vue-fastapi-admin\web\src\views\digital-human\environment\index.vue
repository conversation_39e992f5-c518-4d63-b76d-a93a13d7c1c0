<template>
  <common-page show-footer title="虚拟环境管理">
    <n-card>
      <n-space vertical>
        <n-descriptions bordered label-placement="left" title="环境信息">
          <n-descriptions-item label="Python版本">
            {{ envInfo.python_version }}
          </n-descriptions-item>
          <n-descriptions-item label="虚拟环境路径">
            {{ envInfo.env_path }}
          </n-descriptions-item>
          <n-descriptions-item label="操作系统">
            {{ envInfo.system_info?.os }} ({{ envInfo.system_info?.platform }})
          </n-descriptions-item>
        </n-descriptions>

        <n-divider />
        
        <n-card title="环境变量">
          <n-space vertical>
            <n-form
              ref="formRef"
              :model="envVarForm"
              label-placement="left"
              label-width="auto"
              require-mark-placement="right-hanging"
            >
              <n-form-item label="变量名" path="varName">
                <n-input v-model:value="envVarForm.varName" placeholder="输入环境变量名" />
              </n-form-item>
              <n-form-item label="变量值" path="varValue">
                <n-input v-model:value="envVarForm.varValue" placeholder="输入环境变量值" />
              </n-form-item>
              <n-form-item>
                <n-button type="primary" @click="updateEnvVar">更新环境变量</n-button>
              </n-form-item>
            </n-form>
            
            <n-divider />
            
            <n-descriptions bordered title="当前环境变量">
              <n-descriptions-item v-for="(value, key) in envInfo.env_vars" :key="key" :label="key">
                {{ value }}
              </n-descriptions-item>
            </n-descriptions>
          </n-space>
        </n-card>

        <n-divider />

        <n-space>
          <n-button type="primary" @click="handleRestart">
            重启服务
          </n-button>
          <n-button type="info" @click="handleUpdate">
            更新依赖
          </n-button>
          <n-button @click="refreshInfo">
            刷新信息
          </n-button>
        </n-space>

        <n-divider />

        <n-card title="已安装的包">
          <n-data-table
            :columns="packageColumns"
            :data="envInfo.packages || []"
            :pagination="{ pageSize: 10 }"
          />
        </n-card>
      </n-space>
    </n-card>
  </common-page>
</template>

<script setup>
import { ref, onMounted, h } from 'vue'
import { useMessage } from 'naive-ui'
import { request } from '@/utils'

const message = useMessage()
const envInfo = ref({
  python_version: '',
  env_path: '',
  packages: [],
  system_info: {},
  env_vars: {}
})

const envVarForm = ref({
  varName: '',
  varValue: ''
})

const packageColumns = [
  {
    title: '包名',
    key: 'name',
    width: 200,
  },
  {
    title: '版本',
    key: 'version',
    width: 150,
  }
]

async function getEnvironmentInfo() {
  try {
    const res = await request.get('/environment/info')
    envInfo.value = res
  } catch (error) {
    message.error('获取环境信息失败')
    console.error(error)
  }
}

async function handleRestart() {
  try {
    const res = await request.post('/environment/control', { action: 'restart' })
    message.success(res.message || '重启指令已发送')
  } catch (error) {
    message.error('操作失败')
    console.error(error)
  }
}

async function handleUpdate() {
  try {
    const res = await request.post('/environment/control', { action: 'update' })
    message.success(res.message || '更新指令已发送')
  } catch (error) {
    message.error('操作失败')
    console.error(error)
  }
}

async function updateEnvVar() {
  if (!envVarForm.value.varName || !envVarForm.value.varValue) {
    message.warning('变量名和变量值不能为空')
    return
  }
  
  try {
    const res = await request.post('/environment/update-env', {
      var_name: envVarForm.value.varName,
      var_value: envVarForm.value.varValue
    })
    message.success(res.message || '环境变量已更新')
    // 清空表单
    envVarForm.value.varName = ''
    envVarForm.value.varValue = ''
    // 刷新环境信息
    getEnvironmentInfo()
  } catch (error) {
    message.error('更新环境变量失败')
    console.error(error)
  }
}

function refreshInfo() {
  getEnvironmentInfo()
  message.success('刷新成功')
}

onMounted(() => {
  getEnvironmentInfo()
})
</script>