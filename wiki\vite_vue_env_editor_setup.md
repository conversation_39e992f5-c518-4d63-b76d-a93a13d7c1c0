# 使用 Vite + Vue.js 3 创建 .env 编辑器前端

本文档将指导您如何使用 Vite 和 Vue.js 3 创建一个结构化的前端应用程序来管理服务器上的 `.env` 文件。此前端应用将与您 FastAPI 后端已有的 `/admin/env` API 接口进行交互。

## 前提条件

1.  **Node.js 和 npm/yarn**: 确保您的开发环境中已安装 Node.js (推荐 LTS 版本) 和 npm (或 yarn)。
2.  **后端 API**: 您 FastAPI 应用中的 `/admin/env` GET 和 POST 接口已按之前的说明实现并可访问，包括 `X-Admin-Password` 认证。

## 步骤 1: 初始化 Vite + Vue.js 项目

1.  打开您的终端或命令提示符。
2.  导航到您希望创建前端项目的目录（这可以与您的 FastAPI 后端项目分开）。
3.  运行以下命令来创建一个新的 Vue.js 项目 (您可以将 `env-editor-frontend`替换为您喜欢的项目名称)：

    ```bash
    npm create vite@latest env-editor-frontend -- --template vue
    ```
    或者，如果您使用 yarn:
    ```bash
    yarn create vite env-editor-frontend --template vue
    ```

4.  根据提示，您可以选择是否添加 TypeScript, JSX, Vue Router, Pinia, Vitest, ESLint 等。对于这个简单的 `.env` 编辑器，一个基础的 Vue 安装就足够了。为简单起见，以下示例将不包含 TypeScript 或 Vue Router/Pinia，但您可以根据需要添加它们。

5.  进入项目目录：
    ```bash
    cd env-editor-frontend
    ```

6.  安装依赖：
    ```bash
    npm install
    # 或者
    yarn install
    ```

7.  (可选但推荐) 安装 `axios` 用于 API 请求：
    ```bash
    npm install axios
    # 或者
    yarn add axios
    ```

## 步骤 2: 项目结构和核心文件

您的 Vite + Vue 项目的基本结构如下：

```
env-editor-frontend/
├── public/
├── src/
│   ├── assets/
│   ├── components/  (您可以创建此目录存放子组件)
│   ├── App.vue      (主应用组件)
│   ├── main.js      (Vue 应用入口)
│   └── services/    (建议创建此目录存放 API 服务)
│       └── apiService.js
├── index.html
├── package.json
└── vite.config.js
```

### 2.1 `src/services/apiService.js` (API 通信)

创建一个新文件 `src/services/apiService.js`：

```javascript
import axios from 'axios';

// 后端 API 的基础 URL。如果前端和后端部署在不同域或端口，
// 您可能需要配置 Vite 的代理或确保 CORS 设置正确。
// 假设 FastAPI 在本地 8000 端口运行。
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'; 

export default {
  async getEnv(adminPassword) {
    try {
      const response = await axios.get(`${API_BASE_URL}/admin/env`, {
        headers: {
          'X-Admin-Password': adminPassword,
          'Accept': 'application/json',
        },
      });
      return response.data; // { content: "..." }
    } catch (error) {
      console.error('Error fetching .env:', error.response?.data?.detail || error.message);
      throw new Error(error.response?.data?.detail || '无法加载 .env 文件');
    }
  },

  async updateEnv(adminPassword, newContent) {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/admin/env`,
        { content: newContent },
        {
          headers: {
            'X-Admin-Password': adminPassword,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data; // { message: "..." }
    } catch (error) {
      console.error('Error updating .env:', error.response?.data?.detail || error.message);
      throw new Error(error.response?.data?.detail || '无法更新 .env 文件');
    }
  },
};
```
**注意**: 在项目根目录创建 `.env.development` 或 `.env.local` 文件来配置 `VITE_API_BASE_URL`，例如:
`VITE_API_BASE_URL=http://your-fastapi-server-address`
### 2.2 `src/App.vue` (主应用组件)

替换 `src/App.vue` 的内容如下。由于代码较长，这里会分块展示。

**`App.vue` - Template 部分:**
```vue
<template>
  <div id="app-container">
    <h1>.env 文件编辑器 (Vite + Vue)</h1>

    <div v-if="!configLoaded" class="auth-section">
      <h2>认证</h2>
      <div class="form-group">
        <label for="adminPassword">管理员密码 (X-Admin-Password):</label>
        <input type="password" id="adminPassword" v-model="adminPasswordInput" @keyup.enter="loadEnvConfig" placeholder="输入 .env 中的 ADMIN_PASSWORD">
      </div>
      <button @click="loadEnvConfig" :disabled="loading">
        {{ loading ? '加载中...' : '加载 .env 配置' }}
      </button>
    </div>

    <div v-if="configLoaded && !loadingError" class="editor-section">
      <h2>编辑配置</h2>
      <form @submit.prevent="saveEnvConfig">
        <div v-for="(item, index) in envStructure" :key="index" class="form-group-vue">
          <template v-if="item.blank">
            <hr class="separator">
          </template>
          <template v-else-if="item.sectionComment">
            <h3 class="section-comment">{{ item.sectionComment }}</h3>
          </template>
          <template v-else>
            <label :for="item.key">{{ item.key }}</label>
            <p v-if="item.comment" class="comment-text">{{ item.comment }}</p>
            <select v-if="item.type === 'boolean'" :id="item.key" v-model="envValues[item.key]">
              <option value="true">true</option>
              <option value="false">false</option>
            </select>
            <textarea
              v-else-if="item.type === 'textarea' || (item.key && (item.key.includes('API_KEY') || item.key.includes('SECRET') || item.key.includes('PASSWORD') || item.key === 'SYMBOLS'))"
              :id="item.key"
              v-model="envValues[item.key]"
              rows="3"
            ></textarea>
            <input
              v-else
              :type="getInputType(item.key)"
              :id="item.key"
              v-model="envValues[item.key]"
            >
          </template>
        </div>
        <button type="submit" class="btn-save" :disabled="saving">
          {{ saving ? '保存中...' : '保存到服务器' }}
        </button>
      </form>
    </div>

    <div v-if="serverMessage" :class="['server-message', messageType]">
      <p>{{ serverMessage }}</p>
    </div>
     <div v-if="loadingError" class="server-message error">
      <p>{{ loadingError }}</p>
    </div>

    <div class="danger-zone" v-if="configLoaded || loadingError">
      <h3>⚠️ 重要提示</h3>
      <p>此工具通过 API 与后端交互，直接修改服务器上的 <code>.env</code> 文件。</p>
      <p>修改 <code>.env</code> 文件后，通常需要**手动重启**您的后端应用程序才能使更改生效。</p>
      <p>请确保您的管理员密码安全，并仅在受信任的环境中使用此工具。</p>
    </div>
  </div>
</template>
```
**`App.vue` - Script Setup 部分:**
```vue
<script setup>
import { ref, reactive } from 'vue';
import apiService from './services/apiService'; // 确保路径正确

const adminPasswordInput = ref('');
const configLoaded = ref(false);
const loading = ref(false);
const saving = ref(false);
const serverMessage = ref('');
const messageType = ref('success'); // 'success' or 'error'
const loadingError = ref('');

const envValues = reactive({});

// 与 env_editor.html 中类似的结构，用于渲染表单
// 注意：实际值将从 envValues 中获取，该对象由 API 填充
const envStructure = [
    { key: "BASE_URL" }, { key: "API_KEY" }, { key: "TTS_URL" },
    { key: "REFERENCE_ID", comment: "可选值: man, woman, mid_woman" },
    { key: "ZHIYUN_APP_KEY" }, { key: "ZHIYUN_APP_SECRET" }, { key: "VOICE_NAME" },
    { key: "LOG_LEVEL" }, { key: "MODE" }, { blank: true },
    { key: "MAX_FILE_SIZE" }, { key: "REDIS_HOST" }, { key: "REDIS_PORT" },
    { key: "REDIS_DB" }, { key: "REDIS_PASSWORD" }, { key: "CACHE_EXPIRY" }, { blank: true },
    { key: "ORDER" }, { key: "ADMIN_PASSWORD" }, { blank: true },
    { key: "SEMAPHORE" }, { key: "FASTAPI_PORT" }, { key: "EXPOSE_PORT" },
    { key: "EXPOSE_REDIS_PORT" }, { blank: true },
    { sectionComment: "# 阿里云" },{ key: "DASHSCOPE_API_KEY" }, { blank: true },
    { sectionComment: "# 智谱" },{ key: "ZHIPU_API_KEY" }, { blank: true },
    { sectionComment: "# EDGE_TTS" },{ key: "VOICE_NAME_MAN" }, { key: "VOICE_NAME_WOMAN" }, { blank: true },
    { sectionComment: "# DB" },{ key: "ENABLE_DATABASE", type: "boolean" }, { key: "DB_HOST" },
    { key: "DB_PORT" }, { key: "DB_USER" }, { key: "DB_PASSWORD" },
    { key: "DB_ROOT_PASSWORD" }, { key: "DB_NAME" }, { key: "DB_EXPOSE_PORT" }, { blank: true },
    { sectionComment: "# 首次回复" },{ key: "GREETING_ENABLE", type: "boolean" }, { blank: true },
    { sectionComment: "# 英文首次回复" },{ key: "GREETING_EN", type: "boolean" }, { blank: true },
    { sectionComment: "# 切分长度" },{ key: "CUT_LENGTH" }, { blank: true },
    { sectionComment: "# 切分符号" },{ key: "SYMBOLS", type: "textarea" }
];

function getInputType(key) {
  if (!key) return 'text';
  if (key.includes("PORT") || key.includes("SIZE") || key.includes("EXPIRY") || key.includes("SEMAPHORE") || 
      (key.startsWith("DB_") && !["DB_NAME", "DB_HOST", "DB_USER", "DB_PASSWORD", "DB_ROOT_PASSWORD"].includes(key)) || 
      key === "CUT_LENGTH") {
    return 'number';
  }
  return 'text';
}

function parseEnvContentToReactive(content) {
  const lines = content.split('\\n'); // 注意这里是字面量 \n，不是换行符
  const newValues = {};
  
  // Initialize all keys from envStructure to ensure reactivity and correct order
  envStructure.forEach(item => {
    if (item.key) {
      newValues[item.key] = ''; 
    }
  });

  lines.forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const parts = line.split('=');
      if (parts.length >= 2) {
        const key = parts[0].trim();
        const value = parts.slice(1).join('=').trim();
        if (key in newValues) { // Only populate keys defined in envStructure
          newValues[key] = value;
        }
      }
    }
  });
  
  // Update reactive object
  for (const key in newValues) {
    envValues[key] = newValues[key];
  }
}

async function loadEnvConfig() {
  if (!adminPasswordInput.value) {
    alert("请输入管理员密码！");
    return;
  }
  loading.value = true;
  loadingError.value = '';
  serverMessage.value = '';
  try {
    const data = await apiService.getEnv(adminPasswordInput.value);
    if (data && typeof data.content === 'string') {
      parseEnvContentToReactive(data.content);
      configLoaded.value = true;
    } else {
      throw new Error("从服务器获取的 .env 内容格式不正确。");
    }
  } catch (error) {
    console.error("loadEnvConfig error:", error);
    loadingError.value = error.message || '加载配置失败。';
    configLoaded.value = false; 
  } finally {
    loading.value = false;
  }
}

function buildEnvContentFromReactive() {
  let contentLines = [];
  
  envStructure.forEach(item => {
    if (item.blank) {
      // Add a blank line if the previous line wasn't also a blank or a section comment that implies a break
      if (contentLines.length > 0 && contentLines[contentLines.length -1] !== "") {
        const lastStructItemIndex = envStructure.findIndex(s => s.key === contentLines[contentLines.length -1]?.split('=')[0])
        if(lastStructItemIndex !== -1 && !envStructure[lastStructItemIndex+1]?.sectionComment && !envStructure[lastStructItemIndex]?.sectionComment){
            // Avoid double blank lines if previous was already a sectionComment that implies a break
            if(!envStructure.find(s => s.sectionComment && envStructure.indexOf(s) === envStructure.indexOf(item)-1)){
                 contentLines.push("");
            }
        } else if (contentLines.length > 0 && contentLines[contentLines.length -1] !== "" && !item.sectionComment){
             contentLines.push("");
        }
      }
      return;
    }
    if (item.sectionComment) {
      if (contentLines.length > 0 && contentLines[contentLines.length -1] !== "") {
        contentLines.push(""); // Blank line before section comment
      }
      contentLines.push(item.sectionComment);
      return;
    }

    if (item.key && envValues[item.key] !== undefined) {
      contentLines.push(`${item.key}=${envValues[item.key]}`);
    }
  });
  return contentLines.join('\\n'); // Use literal \n for server
}

async function saveEnvConfig() {
  if (!adminPasswordInput.value) {
    alert("认证已过期或未提供密码，请重新加载配置。");
    return;
  }
  saving.value = true;
  serverMessage.value = '';
  loadingError.value = '';
  const newContent = buildEnvContentFromReactive();
  try {
    const data = await apiService.updateEnv(adminPasswordInput.value, newContent);
    serverMessage.value = data.message || "配置已成功保存到服务器。请手动重启后端应用使更改生效。";
    messageType.value = 'success';
  } catch (error) {
    serverMessage.value = error.message || '保存配置失败。';
    messageType.value = 'error';
  } finally {
    saving.value = false;
  }
}
</script>
```
**`App.vue` - Style 部分:**
```vue
<style scoped>
#app-container {
  max-width: 900px;
  margin: 20px auto;
  padding: 25px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 25px;
}

.auth-section, .editor-section {
  background-color: #fff;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 1px 5px rgba(0,0,0,0.05);
}

.auth-section h2, .editor-section h2 {
  margin-top: 0;
  color: #007bff;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 18px;
}
.form-group-vue { /* Renamed to avoid conflict if global styles exist */
  margin-bottom: 18px;
}


.form-group-vue label {
  display: block;
  margin-bottom: 6px;
  font-weight: bold;
  color: #555;
}

.form-group-vue input[type="text"],
.form-group-vue input[type="password"],
.form-group-vue input[type="number"],
.form-group-vue select,
.form-group-vue textarea {
  width: calc(100% - 20px);
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  transition: border-color 0.3s;
}

.form-group-vue input:focus,
.form-group-vue select:focus,
.form-group-vue textarea:focus {
  border-color: #007bff;
  outline: none;
}

.form-group-vue textarea {
  min-height: 70px;
  resize: vertical;
}

button, .btn-save {
  background-color: #007bff;
  color: white;
  padding: 10px 18px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
  margin-right: 10px;
}

button:hover, .btn-save:hover {
  background-color: #0056b3;
}

button:disabled, .btn-save:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.btn-save {
  background-color: #28a745; /* Green for save */
}
.btn-save:hover {
  background-color: #1e7e34;
}


.server-message {
  padding: 12px;
  margin-top: 20px;
  border-radius: 4px;
  text-align: center;
}

.server-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.server-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.comment-text {
  font-size: 0.85em;
  color: #6c757d;
  margin-top: -3px;
  margin-bottom: 8px;
}

.separator {
  border: none;
  border-top: 1px solid #eee;
  margin: 25px 0;
}

.section-comment {
  font-size: 1.1em;
  color: #0056b3;
  margin-top: 30px;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #007bff;
}

.danger-zone {
  border: 2px solid #dc3545;
  padding: 15px;
  border-radius: 5px;
  margin-top: 30px;
  background-color: #f8d7da;
}
.danger-zone h3 {
  color: #721c24;
  margin-top: 0;
}
</style>
```

### 2.3 `src/main.js` (Vue 应用入口)
通常不需要修改 `main.js`，它会导入 `App.vue` 并挂载应用。
```javascript
import { createApp } from 'vue'
import App from './App.vue'
// 如果您想添加全局 CSS，可以在这里导入
// import './assets/main.css' 

createApp(App).mount('#app')
```

## 步骤 3: 开发和运行前端应用

1.  **启动开发服务器**:
    在 `env-editor-frontend` 项目的根目录下运行：
    ```bash
    npm run dev
    # 或者
    yarn dev
    ```
    Vite 会启动一个开发服务器 (通常在 `http://localhost:5173` 或类似地址)。在浏览器中打开此地址。

2.  **API 地址配置**:
    *   如果您的 FastAPI 后端运行在不同于 `http://localhost:8000` 的地址，或者您在生产环境中部署，请确保 `src/services/apiService.js` 中的 `API_BASE_URL` 配置正确。
    *   您可以在项目根目录创建 `.env.development` (用于开发) 或 `.env.production` (用于生产构建) 文件，并设置 `VITE_API_BASE_URL` 变量，例如：
        ```
        # .env.development
        VITE_API_BASE_URL=http://localhost:8000 
        ```
        Vite 会自动加载这些环境变量。

3.  **测试**:
    *   在前端页面输入正确的管理员密码，点击“加载 .env 配置”。
    *   修改配置项。
    *   点击“保存到服务器”并检查服务器上的 `.env` 文件是否已更新。
    *   **记住：FastAPI 应用需要重启才能使新的 `.env` 配置生效。**

## 步骤 4: 构建生产版本

当您完成开发和测试后，可以构建用于生产部署的静态文件：
```bash
npm run build
# 或者
yarn build
```
此命令会在项目根目录下创建一个 `dist` 文件夹，其中包含了优化过的静态 HTML, CSS, 和 JavaScript 文件。

## 步骤 5: 部署前端应用

`dist` 文件夹中的内容是纯静态的，您可以将其部署到任何静态文件托管服务，例如：

*   Nginx 或 Apache Web 服务器。
*   GitHub Pages, Netlify, Vercel 等静态站点托管平台。
*   **由 FastAPI 应用提供服务**: 您也可以配置您的 FastAPI 应用，使用 `StaticFiles` 来提供 `dist` 目录中的文件。
    例如，在您的 FastAPI 应用的 `app/factory.py` (或类似文件) 中添加：
    ```python
    from fastapi.staticfiles import StaticFiles
    # ... 其他导入 ...

    def create_app():
        app = FastAPI(...)
        # ... 其他配置 ...

        # 假设您的 Vite 构建输出在 'frontend_dist' 目录，
        # 并且这个目录位于 FastAPI 项目的根目录下。
        # 您需要将 Vite 项目的 dist 文件夹内容复制到 FastAPI 项目下的某个位置。
        # 例如，在 FastAPI 项目根目录创建一个名为 'static_frontend' 的文件夹，
        # 将 Vite 构建的 dist 目录内容复制到这里。
        app.mount("/env-editor", StaticFiles(directory="path_to_your_frontend_dist_folder", html=True), name="env-editor-ui")
        
        # ... 其他路由注册 ...
        return app
    ```
    然后，您可以通过访问 FastAPI 服务器的 `/env-editor`路径来访问前端编辑器。确保 `directory` 参数指向您 Vite 构建输出的 `dist` 文件夹的实际路径。

## 重要安全提示

*   **管理员密码**: 确保您的 `ADMIN_PASSWORD` 足够强大且保密。
*   **网络安全**: 仅在受信任的网络环境或通过 HTTPS 连接访问此管理界面。
*   **应用重启**: 修改 `.env` 文件后，务必手动重启您的 FastAPI 应用以使更改生效。

这个基于 Vite + Vue.js 的方案提供了一个更健壮和可维护的前端界面来管理您的 `.env` 配置。