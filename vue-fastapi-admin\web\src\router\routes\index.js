import { Layout } from '@/router/constant'

// 基础路由
const basicRoutes = [
  {
    name: '登录页',
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
    },
    isHidden: true,
  },
  {
    name: '个人中心',
    path: '/profile',
    component: () => import('@/views/profile/index.vue'),
    meta: {
      title: '个人中心',
    },
    isHidden: true,
  },
]

// 错误页面路由
const errorRoutes = [
  {
    name: '404',
    path: '/404',
    component: () => import('@/views/error-page/404.vue'),
    isHidden: true,
  },
  {
    name: '403',
    path: '/403',
    component: () => import('@/views/error-page/403.vue'),
    isHidden: true,
  },
  {
    name: '500',
    path: '/500',
    component: () => import('@/views/error-page/500.vue'),
    isHidden: true,
  },
  // 将匹配所有内容并将其放在 `$route.params.pathMatch` 下
  {
    name: 'NotFound',
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error-page/404.vue'),
    isHidden: true,
  },
]

// 系统管理路由
const systemRoutes = [
  {
    name: '系统管理',
    path: '/system',
    component: Layout,
    redirect: '/system/user',
    meta: {
      title: '系统管理',
      icon: 'carbon:gui-management',
      order: 2,
      hideMenu: false,
    },
    children: [
      {
        name: '用户管理',
        path: 'user',
        component: () => import('@/views/system/user/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'material-symbols:person-outline-rounded',
          order: 1,
          hideMenu: false,
        },
      },
      {
        name: '角色管理',
        path: 'role',
        component: () => import('@/views/system/role/index.vue'),
        meta: {
          title: '角色管理',
          icon: 'carbon:user-role',
          order: 2,
          hideMenu: false,
        },
      },
      {
        name: '菜单管理',
        path: 'menu',
        component: () => import('@/views/system/menu/index.vue'),
        meta: {
          title: '菜单管理',
          icon: 'material-symbols:list-alt-outline',
          order: 3,
          hideMenu: false,
        },
      },
      {
        name: 'API管理',
        path: 'api',
        component: () => import('@/views/system/api/index.vue'),
        meta: {
          title: 'API管理',
          icon: 'ant-design:api-outlined',
          order: 4,
          hideMenu: false,
        },
      },
      {
        name: '部门管理',
        path: 'dept',
        component: () => import('@/views/system/dept/index.vue'),
        meta: {
          title: '部门管理',
          icon: 'mingcute:department-line',
          order: 5,
          hideMenu: false,
        },
      },
      {
        name: '审计日志',
        path: 'auditlog',
        component: () => import('@/views/system/auditlog/index.vue'),
        meta: {
          title: '审计日志',
          icon: 'ph:clipboard-text-bold',
          order: 6,
          hideMenu: false,
        },
      },
    ],
  },
]

export const constantRoutes = [...basicRoutes, ...errorRoutes, ...systemRoutes]