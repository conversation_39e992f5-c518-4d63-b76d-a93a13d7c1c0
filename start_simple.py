#!/usr/bin/env python3
"""
简单启动脚本
"""
import os
import uvicorn

# 设置使用 SQLite3
os.environ["USE_SQLITE"] = "true"

if __name__ == "__main__":
    print("🚀 启动管理系统...")
    print("📍 访问地址: http://localhost:8001")
    print("📖 API文档: http://localhost:8001/docs")
    print("🔐 管理API: http://localhost:8001/api/admin/")
    print("👤 默认账号: admin / 123456")
    print("-" * 50)

    uvicorn.run(
        "app.factory:create_app",
        factory=True,
        host="0.0.0.0",
        port=8001,
        reload=True
    )
