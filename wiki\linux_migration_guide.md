# 项目迁移到 Linux (通过 Docker Compose) 指南

本文档旨在指导您如何将当前基于 Windows 的项目迁移到 Linux 环境，并使用 Docker Compose 进行部署，同时确保 MySQL 数据库的正常使用。

## 1. 准备工作

### 1.1. 备份 Windows 环境下的 MySQL 数据库

在进行任何迁移操作之前，务必备份您当前的 MySQL 数据库。您可以使用 `mysqldump` 工具来完成此操作。

打开命令提示符或 PowerShell，执行以下命令：

```bash
mysqldump -u [您的用户名] -p[您的密码] [您的数据库名] > backup.sql
```

例如：

```bash
mysqldump -u root -pYourPassword tts_db > backup.sql
```

将生成的 `backup.sql` 文件妥善保管，后续将在 Linux 环境中用于恢复数据。

### 1.2. 检查并准备项目文件

确保您的项目文件完整，特别是以下文件：

*   `.env` (包含敏感配置，如数据库密码、API密钥等)
*   `Dockerfile`
*   `docker-compose.yaml`
*   项目源代码

## 2. 在 Linux 环境中配置 Docker 和 Docker Compose

### 2.1. 安装 Docker

根据您的 Linux 发行版，按照 Docker 官方文档的指引安装 Docker Engine。

*   **Ubuntu:** [https://docs.docker.com/engine/install/ubuntu/](https://docs.docker.com/engine/install/ubuntu/)
*   **Debian:** [https://docs.docker.com/engine/install/debian/](https://docs.docker.com/engine/install/debian/)
*   **CentOS:** [https://docs.docker.com/engine/install/centos/](https://docs.docker.com/engine/install/centos/)
*   **Fedora:** [https://docs.docker.com/engine/install/fedora/](https://docs.docker.com/engine/install/fedora/)

安装完成后，启动 Docker 服务并设置为开机自启：

```bash
sudo systemctl start docker
sudo systemctl enable docker
```

### 2.2. 安装 Docker Compose

按照 Docker Compose 官方文档的指引安装 Docker Compose。

*   **Linux (推荐):** [https://docs.docker.com/compose/install/linux/](https://docs.docker.com/compose/install/linux/)

通常，可以通过以下命令下载并安装最新版本的 Docker Compose：

```bash
sudo curl -L "https://github.com/docker/compose/releases/download/$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep -oP '"tag_name": "\K(.*)(?=")')/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

验证安装：

```bash
docker-compose --version
```

## 3. 修改 `docker-compose.yaml` 添加 MySQL 服务

为了在 Docker 环境中运行 MySQL，我们需要在 `docker-compose.yaml` 文件中添加 MySQL 服务。

打开 [`docker-compose.yaml:1`](docker-compose.yaml:1) 文件，并进行如下修改：

```yaml
services:
  app:
    container_name: human_api_v4
    build: .
    restart: always
    ports:
      - "${EXPOSE_PORT:-8288}:${FASTAPI_PORT:-8000}"
    volumes:
      - .:/app
      - audio_cache:/app/audio_cache
    env_file:
      - .env
    environment:
      - REDIS_HOST=redis
      - DB_HOST=mysql  # 新增：指定数据库主机为 docker-compose 中的 mysql 服务
    depends_on:
      - redis
      - mysql  # 新增：依赖 mysql 服务
    networks:
      - app_network

  redis:
    image: redis:alpine
    restart: always
    container_name: redis-v4
    command: redis-server --requirepass ${REDIS_PASSWORD} --bind 0.0.0.0
    ports:
      - "${REDIS_PORT:-6388}:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network

  mysql: # 新增 MySQL 服务
    image: mysql:8.0 # 您可以根据需要选择 MySQL 版本
    container_name: mysql-v4
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD} # 从 .env 文件读取 root 密码
      MYSQL_DATABASE: ${DB_NAME} # 从 .env 文件读取数据库名
      # MYSQL_USER: ${DB_USER} # 通常与 MYSQL_ROOT_PASSWORD 配合使用来创建新用户，或在不设置 MYSQL_ROOT_PASSWORD 时使用
      # MYSQL_PASSWORD: ${DB_PASSWORD} # 同上
      # 注意: 如果您的应用程序使用 root 用户连接 (如您 .env 中 DB_USER=root),
      # 那么 MYSQL_ROOT_PASSWORD 已经设置了 root 用户的密码。
      # 此时不应再为 root 用户显式设置 MYSQL_USER 和 MYSQL_PASSWORD。
      # 如果需要创建额外的非 root 用户供应用连接，则可以取消注释并设置 MYSQL_USER 和 MYSQL_PASSWORD。
    ports:
      - "${DB_EXPOSE_PORT:-3307}:3306" # 将容器的 3306 端口映射到主机的指定端口
    volumes:
      - mysql_data:/var/lib/mysql # 持久化数据库数据
    networks:
      - app_network

volumes:
  redis_data:
  audio_cache:
  mysql_data: # 定义 MySQL 数据卷，用于持久化
    # driver: local # 默认驱动是 local

networks:
  app_network:
    driver: bridge
```

**重要修改点解释：**

*   **`app` 服务:**
    *   在 `environment` 中添加 `DB_HOST=mysql`，这样应用程序会连接到名为 `mysql` 的 Docker 服务。
    *   在 `depends_on` 中添加 `mysql`，确保 `mysql` 服务先于 `app` 服务启动。
*   **新增 `mysql` 服务:**
    *   `image: mysql:8.0`: 指定了 MySQL 镜像版本。您可以根据实际情况选择其他版本。
    *   `environment`:
        *   `MYSQL_ROOT_PASSWORD`: 设置 MySQL `root` 用户的密码。这是必需的，建议从 `.env` 文件中读取。
        *   `MYSQL_DATABASE`: MySQL 服务启动时会自动创建的数据库名称。从 `.env` 文件中读取。
        *   **关于 `MYSQL_USER` 和 `MYSQL_PASSWORD`**：
            *   当您已经设置了 `MYSQL_ROOT_PASSWORD` 来指定 `root` 用户的密码时，并且您的应用程序也打算使用 `root` 用户连接（如您在 `.env` 中设置 `DB_USER=root`），则**不应该**在 `docker-compose.yaml` 中再为 `root` 用户设置 `MYSQL_USER` 和 `MYSQL_PASSWORD`。MySQL 镜像会自动使用 `MYSQL_ROOT_PASSWORD` 来配置 `root` 用户。
            *   如果您希望创建一个**新的、非 `root` 的普通用户**供应用程序连接，那么您可以在 `.env` 中为 `DB_USER` 和 `DB_PASSWORD` 设置这个新用户的凭证，并在 `docker-compose.yaml` 的 `mysql` 服务中通过 `MYSQL_USER` 和 `MYSQL_PASSWORD` 来定义这个新用户及其密码（同时仍然需要 `MYSQL_ROOT_PASSWORD` 来设置 `root` 密码）。
    *   `ports`: 将容器的 `3306` 端口映射到主机的指定端口（默认为 `3307`）。
    *   `volumes`: `mysql_data:/var/lib/mysql` **这是实现数据持久化的关键**。它将 MySQL 容器内部的 `/var/lib/mysql` 目录（MySQL 存储数据的地方）映射到 Docker 管理的一个名为 `mysql_data` 的卷上。
*   **新增 `mysql_data` 卷:** 在 `volumes:` 顶层部分定义 `mysql_data:`。这是一个 Docker 命名卷。

### 3.1. 关于数据持久性 (`docker-compose down` 和数据库数据)

您可能会担心执行 `docker-compose down` 命令会导致数据库数据丢失。**好消息是，由于我们使用了 Docker 命名卷 (`mysql_data`)，默认情况下数据是安全的。**

*   **`docker-compose down` 的默认行为：**
    *   此命令会停止并移除在 `docker-compose.yaml` 中定义的服务所创建的容器、网络和链接。
    *   **但是，它默认不会删除命名卷（Named Volumes）**，例如我们为 MySQL 定义的 `mysql_data` 卷，以及为 Redis 定义的 `redis_data` 卷。
    *   这意味着，当您下次执行 `docker-compose up` 时，MySQL 和 Redis 服务会重新连接到这些已存在的卷，并且之前的数据会完好无损。

*   **如何删除卷（警告：这将永久删除数据！）：**
    *   如果您确实希望在执行 `down` 命令时一并删除所有相关的命名卷（包括 `mysql_data` 和 `redis_data`，从而删除所有数据库和 Redis 数据），您需要使用 `-v` 或 `--volumes` 标志：
      ```bash
      docker-compose down -v
      ```
    *   **请务必谨慎使用此命令，并确保您已经备份了重要数据。**

*   **查看 Docker 卷：**
    *   您可以使用 `docker volume ls` 命令查看 Docker 主机上存在的所有卷。
    *   您可以使用 `docker volume inspect mysql_data` (替换为您的卷名) 查看特定卷的详细信息，包括它在主机上的存储位置。

**总结：只要您不使用 `docker-compose down -v` 命令，您的 `mysql_data` 和 `redis_data` 卷中的数据在执行 `docker-compose down` 后会得到保留。** 尽管如此，定期备份数据库仍然是一个非常重要的好习惯（如第 1.1 节所述）。
## 4. 更新 `.env` 文件

根据您提供的 `.env` 配置，并结合 Docker Compose 的 MySQL 服务设置，您的 `.env` 文件在迁移到 Linux 并使用 Docker Compose 后，需要进行如下调整。

**请将以下内容作为参考，并根据您的实际情况更新或添加到您的 `.env` 文件中：**

```env
# FastAPI 应用端口 (如果您的 .env 中已有，请保留或按需修改)
# FASTAPI_PORT=8000 # 应用程序在容器内监听的端口
# EXPOSE_PORT=8288 # 应用程序暴露给主机的端口

# Database settings for your application (app service in docker-compose)
# 这些设置供 FastAPI 应用程序连接到 docker-compose 中的 mysql 服务使用
DB_HOST=mysql # 重要：此处应为 docker-compose.yaml 中定义的 MySQL 服务名
DB_PORT=3306  # MySQL 服务在容器内部监听的端口
DB_USER=root # 根据您提供的信息
DB_PASSWORD=1314520sm # 根据您提供的信息
DB_NAME=tts_db # 根据您提供的信息
ENABLE_DATABASE=true # 根据您提供的信息

# Database settings for the MySQL service itself (mysql service in docker-compose)
# 这些设置用于初始化 docker-compose 中的 mysql 服务
# MYSQL_ROOT_PASSWORD 是必需的，请设置一个强密码
DB_ROOT_PASSWORD=your_strong_mysql_root_password_here # 请务必修改为一个安全的 MySQL root 密码
# DB_NAME, DB_USER, DB_PASSWORD 已在上方定义，MySQL 服务会使用这些值创建数据库和用户

# (可选) MySQL 服务暴露给主机的端口，如果需要从主机直接访问 MySQL 服务
# DB_EXPOSE_PORT=3307

# Redis settings
REDIS_HOST=redis # docker-compose.yaml 中定义的 Redis 服务名
REDIS_PORT=6379 # Redis 服务在容器内部监听的端口
REDIS_PASSWORD=your_strong_redis_password_here # 请务必修改为一个安全的 Redis 密码
# REDIS_DB=1 # 如果您的应用需要指定 Redis 数据库编号

# ... 其他您项目中原有的 .env 配置 ...
# 例如:
# base_url=...
# api_key=...
# max_file_size=...
# tts_url=...
# reference_id=...
# zhiyun_app_key=...
# zhiyun_app_secret=...
# voice_name=...
# log_level=...
# mode=...
# cache_expiry=...
# order=...
# admin_password=...
# semaphore=...
# dashscope_api_key=...
# voice_name_man=...
# voice_name_woman=...
# greeting_enable=...
# cut_length=...
# symbols=...
```

**重要说明和注意事项：**

1.  **`DB_HOST` 对于应用程序：**
    *   在您的应用程序（即 `app` 服务）连接数据库时，`DB_HOST` **必须设置为 `mysql`** (或者您在 `docker-compose.yaml` 中为 MySQL 服务定义的任何名称)。这是因为在 Docker Compose 创建的网络中，服务可以通过其服务名称相互访问。
    *   您之前在 Windows 上 `.env` 文件中的 `DB_HOST=localhost` 是因为数据库直接运行在本地主机上。在 Docker 环境中，`localhost` 指的是容器本身，而不是 Docker 主机或其他容器。

2.  **`DB_PORT` 对于应用程序：**
    *   应设置为 MySQL 容器内部监听的端口，即 `3306`。

3.  **MySQL 服务环境变量 (`docker-compose.yaml` 中的 `mysql` 服务)：**
    *   `MYSQL_DATABASE: ${DB_NAME}`: Docker MySQL 镜像会使用这个环境变量在启动时自动创建名为 `tts_db` 的数据库。
    *   `MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}`: **这是 MySQL `root` 用户的密码。** 在 `docker-compose.yaml` 的 `mysql` 服务中，这是配置 `root` 用户密码的正确方式。
    *   **关于应用程序连接用户 (`.env` 中的 `DB_USER`, `DB_PASSWORD`)**：
        *   您的应用程序（`app` 服务）将使用 `.env` 文件中定义的 `DB_USER` 和 `DB_PASSWORD` 来连接数据库。
        *   根据您提供的 `.env` 配置，`DB_USER=root` 且 `DB_PASSWORD=1314520sm`。这意味着您的应用程序将尝试以 `root` 用户身份连接 MySQL。
        *   因此，`.env` 文件中的 `DB_ROOT_PASSWORD` 的值**必须**与 `DB_PASSWORD` 的值（当 `DB_USER` 为 `root` 时）相同，即 `1314520sm`。
        *   **重要**：在 `docker-compose.yaml` 的 `mysql` 服务定义中，我们**不应**再为 `root` 用户设置 `MYSQL_USER` 和 `MYSQL_PASSWORD` 环境变量，因为 `MYSQL_ROOT_PASSWORD` 已经处理了 `root` 用户的密码设置。同时为 `root` 用户设置这些会导致您遇到的错误。
        *   如果您的应用程序需要以一个**非 `root` 用户**连接，您应该在 `.env` 中为 `DB_USER` 和 `DB_PASSWORD` 设置该非 `root` 用户的信息，并在 `docker-compose.yaml` 的 `mysql` 服务中通过 `MYSQL_USER` 和 `MYSQL_PASSWORD` 来创建这个用户（同时仍然需要 `MYSQL_ROOT_PASSWORD` 来设置 `root` 密码）。

4.  **密码安全：**
    *   请确保为 `DB_ROOT_PASSWORD` 和 `REDIS_PASSWORD` 设置强密码，并妥善保管您的 `.env` 文件，不要将其提交到公共代码仓库。

5.  **保留原有配置：**
    *   请确保将您项目中其他必要的环境变量（如 API 密钥、日志级别等）也包含在迁移后的 `.env` 文件中。上面示例中已列出一些您 [`settings/config.py:1`](settings/config.py:1) 中定义的变量作为提醒。

正确配置 `.env` 文件对于确保您的应用程序在 Docker 环境中能够顺利连接到数据库和 Redis 服务至关重要。
## 5. 将项目文件和数据库备份传输到 Linux 服务器

使用 `scp`、`rsync` 或其他文件传输工具，将您的整个项目文件夹（包含修改后的 `docker-compose.yaml` 和 `.env` 文件）以及 `backup.sql` 文件传输到您的 Linux 服务器上。

例如，使用 `scp`：

```bash
scp -r /path/to/your/project_folder username@your_linux_server_ip:/path/to/destination
scp /path/to/backup.sql username@your_linux_server_ip:/path/to/destination
```

## 6. 在 Linux 环境中启动服务并恢复数据库

### 6.1. 启动 Docker Compose 服务

在 Linux 服务器上，进入项目目录，然后执行以下命令启动所有服务：

```bash
cd /path/to/your/project_folder
docker-compose up -d --build
```

*   `up`: 创建并启动容器。
*   `-d`: 后台运行。
*   `--build`: 如果 Dockerfile 或其上下文发生更改，则重新构建镜像。

等待所有服务（特别是 `mysql` 服务）成功启动。您可以使用 `docker-compose ps` 查看服务状态。

### 6.2. 恢复 MySQL 数据库

当 `mysql` 服务运行后，您需要将之前备份的 `backup.sql` 文件导入到 Docker 容器中的 MySQL 数据库。

首先，找到 MySQL 容器的 ID 或名称：

```bash
docker ps
```

假设 MySQL 容器的名称是 `mysql-v4` (根据 `docker-compose.yaml` 中的 `container_name`)。

然后，使用以下命令将 `backup.sql` 文件复制到 MySQL 容器内：

```bash
docker cp backup.sql mysql-v4:/tmp/backup.sql
```

接下来，进入 MySQL 容器并执行导入命令：

```bash
docker exec -it mysql-v4 bash
```

在容器的 shell 中，执行以下命令导入数据：

```bash
mysql -u root -p${DB_ROOT_PASSWORD} ${DB_NAME} < /tmp/backup.sql
```

**注意：**

*   `${DB_ROOT_PASSWORD}` 和 `${DB_NAME}` 应该与您在 `.env` 文件中为 MySQL 服务配置的值一致。系统可能会提示您输入 root 密码。
*   如果您的 `backup.sql` 文件很大，这个过程可能需要一些时间。

导入完成后，输入 `exit` 退出容器的 shell。

## 7. 验证应用程序

此时，您的应用程序应该已经在 Linux 环境中通过 Docker Compose 成功运行，并且数据库也已恢复。

通过浏览器或 API 测试工具访问您的应用程序，确保所有功能（特别是与数据库交互的功能）正常工作。

您可以通过以下命令查看应用程序的日志：

```bash
docker-compose logs app
```

或者查看特定服务的日志：

```bash
docker-compose logs mysql
docker-compose logs redis
```

## 8. Aerich 数据库迁移详解

如果您的项目使用 Aerich 进行数据库迁移管理，理解其工作方式和相关命令对于确保数据库结构的一致性至关重要。

### 8.1. `migrations` 文件夹的重要性

项目根目录下的 `migrations` 文件夹是由 Aerich 生成和管理的，它包含了数据库模型的变更历史记录。**这个文件夹非常重要，绝对不应该被删除。** 它记录了数据库从初始状态到当前状态的所有结构变更（例如表的创建、字段的增删改等）。

*   **版本控制：** `migrations` 文件夹使得数据库结构可以像代码一样进行版本控制。
*   **协作开发：** 在团队协作中，不同开发者对数据库模型的修改可以通过迁移文件进行同步。
*   **部署：** 在部署到新环境（如本次迁移到 Linux）或更新现有环境时，Aerich 使用这些迁移文件来确保数据库结构与代码中的模型定义一致。

### 8.2. Aerich 常用命令

您可以通过进入 `app` 容器来执行 Aerich 命令：

```bash
docker-compose exec app bash
```

进入容器后，常用的 Aerich 命令如下：

*   **`aerich init-db`**:
    *   **作用：** 初始化 Aerich，在数据库中创建 `aerich` 表（用于跟踪已应用的迁移版本），并在项目中生成 `migrations` 文件夹和初始配置文件（如果尚不存在）。
    *   **何时使用：** 通常在一个全新的项目开始使用 Aerich 时执行一次。**在迁移现有项目到新环境时，如果 `migrations` 文件夹已存在并且数据库已通过 `backup.sql` 恢复，则通常不需要再次执行 `init-db`。** 如果您在新环境中创建了一个全新的空数据库（而不是从备份恢复），并且希望 Aerich 从头开始管理它，那么可能需要执行此命令。但在此次迁移场景中，我们已经通过 `backup.sql` 恢复了数据和结构，所以数据库中应该已经有了 `aerich` 表。

*   **`aerich migrate [name]`**:
    *   **作用：** 根据您在 Tortoise ORM 模型（例如 [`api_versions/v2/models.py:1`](api_versions/v2/models.py:1)）中所做的更改，自动生成一个新的迁移文件。`[name]` 是可选的，用于给迁移文件一个描述性的名称。
    *   **何时使用：** 当您修改了数据库模型（例如添加新表、修改字段）并希望将这些更改记录为一个新的迁移版本时。

*   **`aerich upgrade`**:
    *   **作用：** 应用所有尚未应用的迁移文件，将数据库结构更新到最新版本。
    *   **何时使用：**
        *   在部署新代码到生产环境或测试环境后。
        *   在拉取了包含新数据库迁移的最新代码后。
        *   **在本次迁移场景中，当您在 Linux 环境中首次启动应用，并且确认数据库已从 `backup.sql` 成功恢复后，可以运行此命令来确保数据库结构与最新的模型定义完全一致。** 即使 `backup.sql` 包含了最新的结构，运行 `aerich upgrade` 也是一个好习惯，它可以验证并应用任何可能在备份后产生的细微模型变更。

*   **`aerich downgrade [-v version]`**:
    *   **作用：** 回滚数据库迁移。默认回滚到上一个版本，使用 `-v version` 可以指定回滚到的特定版本号。
    *   **何时使用：** 当发现某个迁移引入了问题，需要回退到之前的数据库结构时。请谨慎使用，确保您了解回滚操作可能带来的数据影响。

*   **`aerich history`**:
    *   **作用：** 显示所有已生成的迁移文件及其状态（是否已应用）。
    *   **何时使用：** 查看迁移历史记录。

*   **`aerich heads`**:
    *   **作用：** 显示当前最新的迁移版本（即“头部”迁移）。
    *   **何时使用：** 确认当前数据库结构对应的迁移版本。

### 8.3. 迁移到 Linux 后的 Aerich 操作流程

1.  **恢复数据库：** 按照第 6.2 节的步骤，从 `backup.sql` 文件恢复您的 MySQL 数据库。此时，数据库的结构和数据应该与您在 Windows 环境中的状态一致，并且 `aerich` 表也应该包含了已应用的迁移信息。
2.  **启动应用程序：** 使用 `docker-compose up -d --build` 启动所有服务。
3.  **（推荐）运行 `aerich upgrade`：**
    *   进入 `app` 容器：`docker-compose exec app bash`
    *   在容器内执行：`aerich upgrade`
    *   这一步会检查 `migrations` 文件夹中的迁移文件，并与数据库中 `aerich` 表记录的已应用迁移进行比较。如果存在尚未应用的迁移（例如，在您备份数据库之后，代码中有新的模型变更），`aerich upgrade` 会将这些变更应用到数据库。如果数据库结构已经是最新的，该命令不会执行任何操作，但会确认一切同步。

4.  **日常开发与部署：**
    *   当您在本地（或开发环境）修改了 [`api_versions/v2/models.py:1`](api_versions/v2/models.py:1) 文件后：
        1.  在本地（或开发环境的 `app` 容器内）运行 `aerich migrate <migration_name>` 来生成新的迁移文件。
        2.  将代码（包括新生成的迁移文件）提交到版本控制系统。
    *   当您将更新后的代码部署到 Linux 服务器时：
        1.  拉取最新的代码（包含新的迁移文件）。
        2.  重新构建并重启 Docker 服务：`docker-compose up -d --build` (如果 Dockerfile 或依赖有变动) 或 `docker-compose restart app` (如果只是代码更新)。
        3.  进入 `app` 容器：`docker-compose exec app bash`
        4.  在容器内执行：`aerich upgrade` 来应用新的迁移。

**总结：**

*   **`migrations` 文件夹是核心，必须随项目代码一起迁移和版本控制。**
*   在新环境中恢复数据库后，运行 `aerich upgrade` 是一个好习惯，以确保数据库结构与代码模型同步。
*   后续的模型变更应通过 `aerich migrate` 生成迁移文件，并通过 `aerich upgrade` 应用。
## 9. 注意事项和故障排除 (迁移后)

*   **数据持久性与 `docker-compose down`：**
    *   如第 3.1 节所述，由于使用了命名卷 (`mysql_data`, `redis_data`)，标准的 `docker-compose down` 命令**不会**删除您的数据库或 Redis 数据。
    *   **警告：** 只有当您使用 `docker-compose down -v` (或 `docker-compose down --volumes`) 命令时，这些卷才会被删除，导致数据丢失。请谨慎使用 `-v` 标志。
    *   **强烈建议定期备份您的数据库**（例如使用 `mysqldump`），即使数据通过卷进行了持久化，以防止意外删除、卷损坏或其他灾难情况。

*   **文件权限：** 确保 Linux 服务器上的项目文件具有正确的权限，以便 Docker 可以访问它们。特别是在挂载本地目录到容器时（如 `.:/app`），Docker 进程（通常以 root 用户运行，除非另有配置）需要对这些文件有读权限，有时还需要写权限。

*   **端口冲突：** 如果 `.env` 文件中指定的端口（如 `EXPOSE_PORT`, `REDIS_PORT`, `DB_EXPOSE_PORT`）在 Linux 主机上已被占用，您需要更改为其他可用端口，或者停止占用该端口的其他服务。

*   **网络配置：** 确保 Docker 网络配置正确，允许容器之间以及容器与主机之间的通信。`docker-compose.yaml` 中定义的 `app_network` 通常能处理容器间通信。如果遇到连接问题，检查防火墙设置。

*   **环境变量：** 仔细检查 `.env` 文件中的所有环境变量是否正确配置，特别是数据库连接信息 (`DB_HOST=mysql`, `DB_USER`, `DB_PASSWORD`, `DB_NAME`, `DB_ROOT_PASSWORD`)、Redis 连接信息 (`REDIS_HOST=redis`, `REDIS_PASSWORD`)以及任何 API 密钥。不正确的环境变量是导致服务启动失败或连接问题的常见原因。

*   **首次启动和数据库初始化：**
    *   当 `mysql` 服务首次启动且 `mysql_data` 卷为空时，MySQL 镜像会使用 `MYSQL_ROOT_PASSWORD`, `MYSQL_DATABASE`, `MYSQL_USER`, `MYSQL_PASSWORD` (如果提供了后两者用于创建新用户) 来初始化数据库。
    *   之后，您需要从 `backup.sql` 恢复数据（如第 6.2 节）。

*   **日志分析：** 如果遇到问题，请首先查看 Docker Compose 和各个容器的日志，它们通常会提供有用的错误信息。
    *   查看所有服务的日志：`docker-compose logs`
    *   查看特定服务的日志（例如 `app` 或 `mysql`）：`docker-compose logs app` 或 `docker-compose logs mysql`
    *   实时跟踪日志：`docker-compose logs -f app`

*   **Aerich 迁移：** 确保在恢复数据库并启动应用后，运行 `aerich upgrade` (如第 8.3 节所述) 以同步任何可能存在的模型变更。

通过以上步骤和注意事项，您应该能够成功地将您的项目从 Windows 迁移到 Linux，并使用 Docker Compose 进行部署和管理。