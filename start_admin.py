import os
import subprocess
import sys
import threading
import time

def run_backend():
    """运行现有的v2接口后端"""
    print("启动后端服务...")
    # 使用您现有的虚拟环境和启动命令
    subprocess.run(["python", "app.py"], cwd=os.path.dirname(os.path.abspath(__file__)))

def run_frontend():
    """运行vue-fastapi-admin前端"""
    print("启动前端服务...")
    # 获取当前脚本的绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 进入前端目录并启动
    frontend_path = os.path.join(current_dir, "vue-fastapi-admin", "web")
    
    print(f"前端路径: {frontend_path}")
    
    # 检查路径是否存在
    if not os.path.exists(frontend_path):
        print(f"错误: 前端目录不存在: {frontend_path}")
        return
    
    try:
        # 检查npm是否可用
        npm_path = "npm"  # 默认假设npm在PATH中
        
        # 如果是Windows系统，尝试查找npm的完整路径
        if sys.platform == "win32":
            # 常见的npm安装位置
            possible_paths = [
                r"C:\Program Files\nodejs\npm.cmd",
                r"C:\Program Files (x86)\nodejs\npm.cmd",
                os.path.expanduser("~\\AppData\\Roaming\\npm\\npm.cmd")
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    npm_path = path
                    print(f"找到npm: {npm_path}")
                    break
        
        # 确保已安装依赖
        if not os.path.exists(os.path.join(frontend_path, "node_modules")):
            print("正在安装前端依赖...")
            subprocess.run([npm_path, "install"], cwd=frontend_path)
        
        # 启动前端开发服务器
        print(f"使用 {npm_path} 启动前端服务...")
        subprocess.run([npm_path, "run", "dev"], cwd=frontend_path)
    
    except Exception as e:
        print(f"启动前端服务失败: {e}")
        print("请确保已安装Node.js和npm，并且可以在命令行中运行npm命令")
        print("您可以手动进入前端目录并运行以下命令:")
        print(f"cd {frontend_path}")
        print("npm install")
        print("npm run dev")

if __name__ == "__main__":
    # 创建后端线程
    backend_thread = threading.Thread(target=run_backend)
    backend_thread.daemon = True
    
    # 启动后端
    backend_thread.start()
    
    # 给后端一些启动时间
    time.sleep(2)
    
    # 启动前端（主线程）
    run_frontend()