import emoji
import thulac
import hashlib
import uuid
import time
import base64
import orjson
import re
import cn2an
import random
import jwt
from jwt import exceptions
from fastapi import Request
from core.services.v2 import tts_server
from core.logger import logger



async def remove_emojis(text):
    logger.debug(f"{__name__} {text}")
    return "".join(chat for chat in emoji.replace_emoji(text))

def create_hash(key:str):
    hash_key = hashlib.md5(key.encode("utf-8")).hexdigest()
    return hash_key

def get_file_name() -> str:
    """ 获得标准的文件名称 方便统一管理 """
    filename = create_hash(f"{uuid.uuid4().hex}_{int(time.time()*1000)}")
    logger.debug(f"filename: {filename}")
    return filename

# 失败时候的音频
def random_voice(*, voice_id):
    import os
    from settings.config import FAIL_DIR

    audio_list = os.listdir(FAIL_DIR)
    man_audio_list = [audio for audio in audio_list if audio.startswith("man")]
    woman_audio_list = [audio for audio in audio_list if audio.startswith("woman")]
    if voice_id == "man":
        return f"fail/{random.choice(man_audio_list)}"
    elif voice_id == "woman":
        return f"fail/{random.choice(woman_audio_list)}"



def get_user_id(access_token:str) -> str:
    try:
        decode = jwt.decode(access_token, options={"verify_signature": False})
        return {
            "status": "success",
            "user_id": decode.get("user_id"),
        }
    except exceptions.ExpiredSignatureError:
        return {
            "status": "error",
            "message": "token已过期"
        }
    except exceptions.InvalidTokenError:
        return {
            "status": "error",
            "message": "token无效"
        }

# 数字转中文读音（整数 + 小数）
def number_to_chinese_readable(number_str: str) -> str:
    if '.' in number_str:
        integer, decimal = number_str.split('.', 1)
        int_part = cn2an.an2cn(int(integer)) if integer else '零'
        decimal_part = ''.join([cn2an.an2cn(int(d)) for d in decimal])
        return f"{int_part}点{decimal_part}"
    else:
        return cn2an.an2cn(int(number_str))

def normalize_text_numbers(text: str) -> str:
    def replacer(match):
        num = match.group(0)
        try:
            return number_to_chinese_readable(num)
        except Exception:
            return num  # fallback
    # 匹配整数或小数（包括金额、序号等）
    return re.sub(r'\d+(\.\d+)?', replacer, text)


# --------------------------- 分词开始 ---------------------------
# 初始化分词器
cut_tool = thulac.thulac(seg_only=True)

# 语义断点词开头
semantic_break_points = [
    "不妨考虑一下", "您可以", "我不太了解", "我觉得", "我认为", "你可能是", 
    "这其实是", "事实上", "其实", "不过", "但是", "然而", "所以", "因此"
]

def cut_words(text: str):
    # 1. 优先处理语义开头的匹配（如果开头是这些词，就作为第一段）
    for phrase in semantic_break_points:
        if text.startswith(phrase) and len(text) > len(phrase) + 4:
            return [phrase, text[len(phrase):].lstrip("，, ")]

    # 2. 分词切句（thulac）
    words = cut_tool.cut(text, text=True).split()

    chunks = []
    buffer = ""
    for word in words:
        buffer += word

        # 遇到停顿符号（模拟朗读断句），或者长度超过一定值
        if word in ["，", "。", "？", "！"] or len(buffer) >= 12:
            chunks.append(buffer.strip())
            buffer = ""

    if buffer:
        chunks.append(buffer.strip())

    # 合并太短的句子（避免出现“我”这种单独一段）
    merged = []
    for chunk in chunks:
        if merged and len(chunk) <= 2:
            merged[-1] += chunk
        else:
            merged.append(chunk)

    return merged

# --------------------------- 分词结束 ---------------------------

# --------------------------- 其他分词方案 ----------------------
async def greeting(request: Request):
    from settings.config import GREETING_LIST, GREETING_LIST_EN
    from settings.config import settings
    from utils.tts_tools import tts_servers

    if settings.greeting_enable:
        GREETING_LIST = GREETING_LIST_EN if settings.greeting_en else GREETING_LIST
        # 先播一句问候语，立即响应
        greeting = random.choice(GREETING_LIST)
        tts_url = await tts_servers(func_name=settings.tts_service,request=request, text=greeting)
        greeting_event = {
            "event": "message",
            "answer": greeting,
            'greeting': True,
            "status": "ok",
            "url": tts_url
        }
        yield f"data: {orjson.dumps(greeting_event).decode()}\n\n".encode()
# --------------------------- 其他分词方案 ----------------------

# --------------------------- 去特殊符号 开始 ---------------------------
def clean_text_for_tts(text: str) -> str:
    # 去除 markdown 列表项、序号、过多感叹号等
    text = re.sub(r'^\s*[-*]\s*', '', text, flags=re.MULTILINE)  # 移除 - 或 * 开头
    text = re.sub(r'^\s*\d+[\.\、]\s*', '', text, flags=re.MULTILINE)  # 移除 1. 2、这种
    text = re.sub(r'[!！]{2,}', '！', text)  # 替换多感叹号
    text = re.sub(r'^\s*[!！]+', '', text, flags=re.MULTILINE)  # 去除行首感叹号
    text = re.sub(r'^\s*#+\s*', '', text, flags=re.MULTILINE)  # 移除 Markdown 标题符号 #
    return text.strip()


# --------------------------- 去特殊符号 结束 ---------------------------