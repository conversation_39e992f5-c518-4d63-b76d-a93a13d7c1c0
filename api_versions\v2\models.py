from tortoise import fields, models

class App(models.Model):
    """
    独立的应用实体
    - name: 应用名称（唯一）
    - description: 应用描述
    """
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=255, unique=True)
    description = fields.CharField(max_length=255)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    api_key = fields.CharField(max_length=255, unique=True)

    class Meta:
        table = "app"
        table_description = "应用主表"

class Device(models.Model):
    """
    设备实体
    - name: 设备名称（唯一）
    - apps: 多对多关联到应用（通过DeviceApp中间表）
    """
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=255, unique=True)
    description = fields.Char<PERSON><PERSON>(max_length=255)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    is_active = fields.BooleanField(default=True)

    # 多对多关系定义
    apps = fields.ManyToManyField(
        "models.App",
        through="device_app",
        related_name="devices",
        forward_key="app_id",
        backward_key="device_id",
        through_relation_name="device",  # 新增关键配置
        through_reverse_relation_name="app"  # 新增关键配置
    )

    class Meta:
        table = "device"
        table_description = "设备表"

class DeviceApp(models.Model):
    """
    设备与应用关联表（中间表）
    - 包含关系专属字段：api_key
    """
    id = fields.IntField(pk=True)
    device = fields.ForeignKeyField(
        "models.Device", 
        related_name="device_apps",
        on_delete=fields.CASCADE
    )
    app = fields.ForeignKeyField(
        "models.App",
        related_name="app_devices",
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "device_app"
        table_description = "设备应用关联表"
        unique_together = (("device", "app"),)  # 确保唯一关联
