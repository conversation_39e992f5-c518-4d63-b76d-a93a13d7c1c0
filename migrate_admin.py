#!/usr/bin/env python3
"""
管理系统数据库迁移脚本
"""
import asyncio
from tortoise import Tortoise
from settings.tortoise_config import TORTOISE_ORM
from api_versions.admin.init_data import init_admin_data


async def migrate_admin_tables():
    """创建管理系统数据表"""
    print("🔄 正在连接数据库...")
    await Tortoise.init(config=TORTOISE_ORM)
    
    print("🔄 正在生成管理系统数据表...")
    await Tortoise.generate_schemas()
    
    print("🔄 正在初始化管理系统数据...")
    await init_admin_data()
    
    print("✅ 管理系统数据库迁移完成!")
    print("👤 默认管理员账号: admin / 123456")
    
    await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(migrate_admin_tables())
