from tortoise.queryset import Q
from .models import Admin<PERSON>ser, AdminRole, AdminMenu, AdminApi, AdminDept, MenuType, MethodType
from .core import PasswordUtils
from core.logger import logger


async def init_admin_superuser():
    """初始化超级管理员"""
    user_exists = await AdminUser.exists()
    if not user_exists:
        admin_user = await AdminUser.create(
            username="admin",
            email="<EMAIL>",
            alias="超级管理员",
            password=PasswordUtils.get_password_hash("123456"),
            is_active=True,
            is_superuser=True,
        )
        logger.info(f"创建超级管理员: {admin_user.username}")


async def init_admin_menus():
    """初始化管理菜单"""
    menu_exists = await AdminMenu.exists()
    if not menu_exists:
        # 系统管理目录
        system_menu = await AdminMenu.create(
            name="系统管理",
            path="/system",
            component="Layout",
            menu_type=MenuType.CATALOG,
            icon="ph:gear-bold",
            order=1,
            parent_id=0,
            is_hidden=False,
            keepalive=True
        )
        
        # 用户管理
        await AdminMenu.create(
            name="用户管理",
            path="/system/user",
            component="/system/user/index",
            menu_type=MenuType.MENU,
            icon="ph:user-list-bold",
            order=1,
            parent_id=system_menu.id,
            is_hidden=False,
            keepalive=True
        )
        
        # 角色管理
        await AdminMenu.create(
            name="角色管理",
            path="/system/role",
            component="/system/role/index",
            menu_type=MenuType.MENU,
            icon="ph:users-bold",
            order=2,
            parent_id=system_menu.id,
            is_hidden=False,
            keepalive=True
        )
        
        # 菜单管理
        await AdminMenu.create(
            name="菜单管理",
            path="/system/menu",
            component="/system/menu/index",
            menu_type=MenuType.MENU,
            icon="ph:tree-structure-bold",
            order=3,
            parent_id=system_menu.id,
            is_hidden=False,
            keepalive=True
        )
        
        # API管理
        await AdminMenu.create(
            name="API管理",
            path="/system/api",
            component="/system/api/index",
            menu_type=MenuType.MENU,
            icon="ph:code-bold",
            order=4,
            parent_id=system_menu.id,
            is_hidden=False,
            keepalive=True
        )
        
        # 部门管理
        await AdminMenu.create(
            name="部门管理",
            path="/system/dept",
            component="/system/dept/index",
            menu_type=MenuType.MENU,
            icon="ph:buildings-bold",
            order=5,
            parent_id=system_menu.id,
            is_hidden=False,
            keepalive=True
        )
        
        # 工作台
        await AdminMenu.create(
            name="工作台",
            path="/dashboard",
            component="/dashboard/index",
            menu_type=MenuType.MENU,
            icon="ph:house-bold",
            order=0,
            parent_id=0,
            is_hidden=False,
            keepalive=True
        )
        
        logger.info("初始化管理菜单完成")


async def init_admin_apis():
    """初始化管理API"""
    api_exists = await AdminApi.exists()
    if not api_exists:
        # 基础API
        base_apis = [
            # 认证相关
            ("/api/admin/auth/login", "POST", "用户登录", "认证管理"),
            ("/api/admin/auth/user_info", "GET", "获取用户信息", "认证管理"),
            ("/api/admin/auth/menus", "GET", "获取用户菜单", "认证管理"),
            
            # 用户管理
            ("/api/admin/users", "GET", "获取用户列表", "用户管理"),
            ("/api/admin/users", "POST", "创建用户", "用户管理"),
            ("/api/admin/users/{user_id}", "PUT", "更新用户", "用户管理"),
            ("/api/admin/users/{user_id}", "DELETE", "删除用户", "用户管理"),
            ("/api/admin/users/{user_id}/password", "PUT", "修改密码", "用户管理"),
            
            # 角色管理
            ("/api/admin/roles", "GET", "获取角色列表", "角色管理"),
            ("/api/admin/roles", "POST", "创建角色", "角色管理"),
            ("/api/admin/roles/{role_id}", "PUT", "更新角色", "角色管理"),
            ("/api/admin/roles/{role_id}", "DELETE", "删除角色", "角色管理"),
            ("/api/admin/roles/{role_id}/permissions", "PUT", "更新角色权限", "角色管理"),
            
            # 菜单管理
            ("/api/admin/menus", "GET", "获取菜单列表", "菜单管理"),
            ("/api/admin/menus", "POST", "创建菜单", "菜单管理"),
            ("/api/admin/menus/{menu_id}", "PUT", "更新菜单", "菜单管理"),
            ("/api/admin/menus/{menu_id}", "DELETE", "删除菜单", "菜单管理"),
            
            # API管理
            ("/api/admin/apis", "GET", "获取API列表", "API管理"),
            ("/api/admin/apis", "POST", "创建API", "API管理"),
            ("/api/admin/apis/{api_id}", "PUT", "更新API", "API管理"),
            ("/api/admin/apis/{api_id}", "DELETE", "删除API", "API管理"),
            ("/api/admin/apis/sync", "POST", "同步API", "API管理"),
            
            # 部门管理
            ("/api/admin/depts", "GET", "获取部门列表", "部门管理"),
            ("/api/admin/depts", "POST", "创建部门", "部门管理"),
            ("/api/admin/depts/{dept_id}", "PUT", "更新部门", "部门管理"),
            ("/api/admin/depts/{dept_id}", "DELETE", "删除部门", "部门管理"),
        ]
        
        for path, method, summary, tags in base_apis:
            await AdminApi.create(
                path=path,
                method=method,
                summary=summary,
                tags=tags
            )
        
        logger.info(f"初始化{len(base_apis)}个管理API")


async def init_admin_roles():
    """初始化管理角色"""
    role_exists = await AdminRole.exists()
    if not role_exists:
        # 创建管理员角色
        admin_role = await AdminRole.create(
            name="管理员",
            desc="系统管理员角色，拥有所有权限",
        )
        
        # 创建普通用户角色
        user_role = await AdminRole.create(
            name="普通用户",
            desc="普通用户角色，拥有基础权限",
        )
        
        # 分配所有API给管理员角色
        all_apis = await AdminApi.all()
        await admin_role.apis.add(*all_apis)
        
        # 分配所有菜单给管理员角色
        all_menus = await AdminMenu.all()
        await admin_role.menus.add(*all_menus)
        
        # 为普通用户分配基本API和菜单
        basic_apis = await AdminApi.filter(
            Q(method="GET") | Q(tags="认证管理")
        )
        await user_role.apis.add(*basic_apis)
        
        # 普通用户只能看到工作台
        dashboard_menu = await AdminMenu.filter(path="/dashboard").first()
        if dashboard_menu:
            await user_role.menus.add(dashboard_menu)
        
        logger.info("初始化管理角色完成")


async def init_admin_depts():
    """初始化部门"""
    dept_exists = await AdminDept.exists()
    if not dept_exists:
        # 创建根部门
        root_dept = await AdminDept.create(
            name="总公司",
            desc="公司总部",
            order=1,
            parent_id=0
        )
        
        # 创建子部门
        await AdminDept.create(
            name="技术部",
            desc="技术研发部门",
            order=1,
            parent_id=root_dept.id
        )
        
        await AdminDept.create(
            name="市场部",
            desc="市场营销部门",
            order=2,
            parent_id=root_dept.id
        )
        
        await AdminDept.create(
            name="人事部",
            desc="人力资源部门",
            order=3,
            parent_id=root_dept.id
        )
        
        logger.info("初始化部门完成")


async def init_admin_data():
    """初始化所有管理数据"""
    try:
        await init_admin_superuser()
        await init_admin_menus()
        await init_admin_apis()
        await init_admin_roles()
        await init_admin_depts()
        logger.info("管理系统初始化完成")
    except Exception as e:
        logger.error(f"管理系统初始化失败: {e}")
        raise
