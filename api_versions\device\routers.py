from fastapi import APIRouter, status, HTTPException
from .schema import DeviceCreateSchema, DeviceUpdateSchema, AppWithKeySchema, Device_Pydantic, App_Pydantic, DeviceWithAppsSchema
from api_versions.v2.models import Device, App


routers = APIRouter()


# 创建设备
@routers.post("/devices", status_code=status.HTTP_201_CREATED, include_in_schema=False)
async def create_device(device_data: DeviceCreateSchema):
    try:
        device = await Device.create(**device_data.model_dump())
        return await Device_Pydantic.from_tortoise_orm(device)
    except  Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建设备失败: {str(e)}"
        )

# 获取全部设备
@routers.get("/devices", description="获取全部设备")
async def get_all_devices():
    return await Device_Pydantic.from_queryset(Device.all())


# 获取设备详情接口
@routers.get("/devices/{device_id}", response_model=DeviceWithAppsSchema, description="获取设备关联的全部应用")
async def get_one_device(device_id: str):
    # 使用正确的查询方式（根据name查询）
    device = await (
        Device.filter(name=device_id)
        .prefetch_related("apps")  # 预加载关联应用
        .first()
    )
    
    if not device:
        raise HTTPException(status_code=404, detail="设备不存在")
    
    # 获取设备基础信息（使用Pydantic v2语法）
    device_data = Device_Pydantic.from_orm(device)
    
    # 获取完整关联应用（包含中间表字段）
    related_apps = await device.apps.all().prefetch_related("app_devices")
    
    # 构建响应数据
    apps = [
        AppWithKeySchema(
            id=app.id,
            name=app.name,
            description=app.description,
            api_key=app.api_key,  # 应用自身的api_key
            created_at=app.created_at,
            updated_at=app.updated_at
        ) for app in related_apps
    ]
    
    return DeviceWithAppsSchema(
        **device_data.model_dump(),
        apps=apps
    )

@routers.put("/devices/{device_id}", include_in_schema=False)
async def update_device(device_id:int, device_data: DeviceUpdateSchema):
    device = await Device.get_or_none(pk=device_id)
    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="设备不存在"
        )

    update_data = device_data.model_dump(exclude_unset=True)
    await device.update_from_dict(update_data).save()
    return await Device_Pydantic.from_tortoise_orm(device)

@routers.get("/apps", include_in_schema=False)
async def get_all_apps():
    return await App_Pydantic.from_queryset(App.all())

