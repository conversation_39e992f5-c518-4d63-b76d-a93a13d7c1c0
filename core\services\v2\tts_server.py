import aiohttp
import asyncio
import edge_tts
import aiofiles
import functools
import tempfile
from pathlib import Path
import concurrent.futures
from pydub import AudioSegment
from io import BytesIO
from settings.config import AUDIO_DIR

from core.dependencies import urls
from settings.config import settings
from core.logger import logger
from core.decorators.async_tools import async_timer
import functools



# 建立局部线程池（最多并发 8 个音频处理任务）
TTS_THREAD_POOL = concurrent.futures.ThreadPoolExecutor(max_workers=8)

# @async_timer
async def text_to_audio_(*, request, text, **kwargs):
    from utils.tools import get_file_name

    reference_id = kwargs.get('reference_id') or request.state.reference_id

    # 可选：构造缓存 key（建议用于后续 Redis 缓存）
    # cache_key = f"tts_cache:{reference_id}:{hashlib.md5(text.encode()).hexdigest()}"
    # cached_url = await redis_client.get(cache_key)
    # if cached_url:
    #     return cached_url.decode()

    data = {
        "text": text,
        "reference_id": reference_id or settings.reference_id,
        "seed": 42,
        "normalize": True,
        "chunk_length": 100,
        "temperature": 0.6,
        "top_p": 0.8,
        "prosody": {"speed": 2.0}
    }

    logger.debug(f"{__name__} data: {data}")

    async with aiohttp.ClientSession() as session:
        async with session.post(url=urls['text-to-audio'], json=data) as resp:
            audio_bytes = await resp.read()

    loop = asyncio.get_event_loop()

    try:
        audio_segment = await loop.run_in_executor(
            TTS_THREAD_POOL,
            functools.partial(AudioSegment.from_wav, BytesIO(audio_bytes))
        )
    except Exception as e:
        raise ValueError(f"音频解析失败，请检查API返回数据格式 : [{text}]") from e

    buffer = BytesIO()
    await loop.run_in_executor(
        TTS_THREAD_POOL,
        functools.partial(
            audio_segment.export,
            buffer,
            format="wav",
            codec="pcm_s16le",
            parameters=["-ar", "16000", "-ac", "1"]
        )
    )
    buffer.seek(0)

    file_name = f"{get_file_name()}.wav"
    async with aiofiles.open(AUDIO_DIR / file_name, "wb") as f:
        await f.write(buffer.read())

    url = request.url_for("audio_files", path=file_name)

    # 可选：设置缓存
    # await redis_client.setex(cache_key, settings.cache_expiry, str(url))

    return str(url)

async def text_to_audio_aliyun(*, request, text, voice="longxiaochun", **kwargs):
    from utils.tools import get_file_name

    """
    阿里云语音合成
    """
    import dashscope
    from dashscope.audio.tts_v2 import SpeechSynthesizer
    import tempfile
    
    reference_id = kwargs.get('reference_id') or request.state.reference_id
    if reference_id.lower() == "man":
        voice = "longxiang"
    else:
        voice = "longxiaochun"

    # 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将apiKey替换为自己的API Key
    dashscope.api_key = settings.dashscope_api_key
    model = "cosyvoice-v1"
    synthesizer = SpeechSynthesizer(model=model, voice=voice)
    audio = synthesizer.call(text)
    print('requestId: ', synthesizer.get_last_request_id())

    # 首先将获取的音频数据保存为临时文件
    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
        temp_file.write(audio)
        temp_file_path = temp_file.name
    
    # 然后使用pydub从文件加载音频（让pydub自动检测格式）
    audio_segment = AudioSegment.from_file(temp_file_path)
    # 转换为16kHz单声道
    audio_segment = audio_segment.set_frame_rate(16000).set_channels(1)
    
    buffer = BytesIO()
    audio_segment.export(
        buffer,
        format="wav",
        codec="pcm_s16le",
        parameters=["-ar", "16000", "-ac", "1"]
    )
    buffer.seek(0)
    
    file_name = f"{get_file_name()}.wav"
    async with aiofiles.open(AUDIO_DIR / file_name, 'wb') as f:
        await f.write(buffer.read())
        url = request.url_for("audio_files", path=file_name)
        # 删除临时文件
        import os
        os.unlink(temp_file_path)
        return str(url)

# 通过ffmpeg处理音频速度
async def text_to_audio_ffmpeg_speed(*, request, text, **kwargs):
    from utils.tools import get_file_name

    reference_id = kwargs.get('reference_id') or request.state.reference_id
    prosody_speed = settings.local_tts_speed  # 默认语速倍速

    data = {
        "text": text,
        "reference_id": reference_id or settings.reference_id,
        "seed": 42,
        "normalize": True,
        "chunk_length": 100,
        "temperature": 0.6,
        "top_p": 0.8
    }

    logger.debug(f"{__name__} data: {data}")

    # 请求 TTS 接口
    async with aiohttp.ClientSession() as session:
        async with session.post(url=urls['text-to-audio'], json=data) as resp:
            audio_bytes = await resp.read()

    loop = asyncio.get_event_loop()

    try:
        # 写入原始音频到临时文件
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_input:
            temp_input.write(audio_bytes)
            input_path = Path(temp_input.name)

        output_path = input_path.with_name(input_path.stem + "_fast.wav")

        # FFmpeg 命令：变速、转采样、单声道
        cmd = [
            "ffmpeg", "-y",
            "-loglevel", "quiet",
            "-i", str(input_path),
            "-filter:a", f"atempo={prosody_speed}",
            "-ar", "16000",
            "-ac", "1",
            str(output_path)
        ]

        process = await asyncio.create_subprocess_exec(*cmd)
        await process.communicate()

        # 读取变速处理后的音频
        buffer = BytesIO(output_path.read_bytes())

        # 清理临时文件
        input_path.unlink(missing_ok=True)
        output_path.unlink(missing_ok=True)

    except Exception as e:
        raise ValueError(f"音频处理失败（FFmpeg）：[{text}]") from e

    # 保存最终音频到本地
    file_name = f"{get_file_name()}.wav"
    async with aiofiles.open(AUDIO_DIR / file_name, "wb") as f:
        await f.write(buffer.read())

    url = request.url_for("audio_files", path=file_name)
    return str(url)

async def text_to_audio_edge(*, request, text: str, **kwargs):
    from utils.tools import get_file_name

    """微软edge_tts"""
    # 配置参数处理
    reference_id = kwargs.get('reference_id') or request.state.reference_id
    voice = settings.voice_name_man if reference_id.lower() == "man" else settings.voice_name_woman

    rate = kwargs.get("rate")

    # 生成临时文件
    communicate = edge_tts.Communicate(text=text, voice=voice, rate=rate)
    file_stem = get_file_name()
    
    # 使用上下文管理器处理文件
    async with aiofiles.tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as tmp_mp3:
        await communicate.save(tmp_mp3.name)
        
        # 音频格式转换
        audio = AudioSegment.from_file(tmp_mp3.name)
        audio = audio.set_channels(1).set_frame_rate(16000).set_sample_width(2)
        
        # 保存最终文件
        wav_path = AUDIO_DIR / f"{file_stem}.wav"
        audio.export(wav_path, format="wav")

    # 返回URL
    return str(request.url_for("audio_files", path=wav_path.name))

