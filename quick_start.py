#!/usr/bin/env python3
"""
快速启动脚本 - 使用 SQLite3 数据库
"""
import asyncio
import uvicorn
import os
from tortoise import Tortoise
# from app.factory import create_app  # 不需要直接导入
from settings.config import settings
from settings.sqlite_config import TORTOISE_ORM
from api_versions.admin.init_data import init_admin_data

# 设置使用 SQLite3
os.environ["USE_SQLITE"] = "true"


async def setup_database():
    """设置数据库"""
    print("🔄 正在初始化 SQLite3 数据库...")
    
    # 初始化 Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 生成数据库表
    await Tortoise.generate_schemas()
    print("✅ 数据库表创建完成")
    
    # 初始化管理系统数据
    await init_admin_data()
    print("✅ 管理系统数据初始化完成")
    
    # 关闭连接
    await Tortoise.close_connections()


def main():
    """主函数"""
    print("🚀 快速启动管理系统...")
    print("=" * 50)
    
    # 设置数据库
    print("📦 正在设置数据库...")
    asyncio.run(setup_database())
    
    print("=" * 50)
    print("🎉 数据库设置完成！")
    print()
    print("📍 系统信息:")
    print(f"   - 数据库: SQLite3 (db.sqlite3)")
    print(f"   - 服务地址: http://localhost:{settings.fastapi_port}")
    print(f"   - API文档: http://localhost:{settings.fastapi_port}/docs")
    print(f"   - 管理API: http://localhost:{settings.fastapi_port}/api/admin/")
    print()
    print("🔐 默认管理员账号:")
    print("   - 用户名: admin")
    print("   - 密码: 123456")
    print()
    print("🎨 前端启动 (可选):")
    print("   cd vue-fastapi-admin/web")
    print("   npm install")
    print("   npm run dev")
    print("   前端地址: http://localhost:3100")
    print()
    print("=" * 50)
    print("🚀 正在启动服务器...")
    
    # 启动服务器
    uvicorn.run(
        "app.factory:create_app",
        factory=True,
        host="0.0.0.0",
        port=settings.fastapi_port,
        reload=True,
        log_level="info"
    )


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 检查端口是否被占用")
        print("2. 检查依赖是否正确安装: pip install -r requirements.txt")
        print("3. 检查文件权限")
