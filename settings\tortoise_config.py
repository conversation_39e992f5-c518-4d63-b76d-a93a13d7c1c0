from settings.config import settings


TORTOISE_ORM = {
    "connections": {
        # SQLite数据库配置 - 更简单，无需额外配置
        "default": {
            "engine": "tortoise.backends.sqlite",
            "credentials": {"file_path": "db.sqlite3"}
        }
        # MySQL配置（如需要可取消注释）
        # "default": rf"mysql://{settings.db_user}:{settings.db_password}@{settings.db_host}:{settings.db_port}/{settings.db_name}"
    },
    "apps": {
        # 你的应用模型
        "models": {
            "models": ["api_versions.v2.models", "api_versions.admin.models"],  # 指向你的模型文件（如 models.py）
            "default_connection": "default"
        },
        # 必须添加 aerich 专用配置
        "aerich": {
            "models": ["aerich.models"],  # 固定写法
            "default_connection": "default"
        }
    },
    "use_tz": False,
    "timezone": "Asia/Shanghai"
}

